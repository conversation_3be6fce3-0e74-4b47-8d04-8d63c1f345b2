import Notification from "../models/Notification.js";
import User from "../models/User.js";
import Task from "../models/Task.js";
import EmailService from "../services/emailService.js";

/**
 * Notification Service - Centralized notification creation and management
 */
class NotificationService {
  
  /**
   * Create a task assignment notification
   */
  static async createTaskAssignmentNotification(taskData, assignedUserIds, creatorId) {
    try {
      const creator = await User.findById(creatorId).select("name");
      
      for (const userId of assignedUserIds) {
        // Skip creating notification for the creator if they assigned to themselves
        if (userId.toString() === creatorId.toString()) continue;
        
        await Notification.createNotification({
          recipient: userId,
          sender: creatorId,
          type: "TASK_ASSIGNED",
          title: "New Task Assigned",
          message: `${creator.name} assigned you a new task: "${taskData.title}"`,
          priority: taskData.priority === "High" ? "HIGH" : taskData.priority === "Medium" ? "MEDIUM" : "LOW",
          relatedTask: taskData._id,
          metadata: {
            taskTitle: taskData.title,
            taskPriority: taskData.priority,
            dueDate: taskData.dueDate,
            creatorName: creator.name
          },
          actions: [
            {
              label: "View Task",
              action: "VIEW_TASK",
              style: "PRIMARY"
            }
          ]
        });
      }

      // Send email notifications
      await EmailService.sendTaskAssignmentNotification(taskData, assignedUserIds, creatorId);

      console.log(`Created task assignment notifications for ${assignedUserIds.length} users`);
    } catch (error) {
      console.error("Error creating task assignment notifications:", error);
      throw error;
    }
  }

  /**
   * Create a task completion notification
   */
  static async createTaskCompletionNotification(task, completedByUserId) {
    try {
      const completedBy = await User.findById(completedByUserId).select("name");
      
      // Notify creator if someone else completed their task
      if (task.createdBy.toString() !== completedByUserId.toString()) {
        await Notification.createNotification({
          recipient: task.createdBy,
          sender: completedByUserId,
          type: "TASK_COMPLETED",
          title: "Task Completed",
          message: `${completedBy.name} completed the task: "${task.title}"`,
          priority: "MEDIUM",
          relatedTask: task._id,
          metadata: {
            taskTitle: task.title,
            completedBy: completedBy.name,
            completedAt: new Date()
          },
          actions: [
            {
              label: "View Task",
              action: "VIEW_TASK",
              style: "PRIMARY"
            }
          ]
        });
      }

      // Notify other assigned users (excluding the one who completed it)
      if (task.assignedTo && task.assignedTo.length > 0) {
        for (const assignedUserId of task.assignedTo) {
          if (assignedUserId.toString() !== completedByUserId.toString()) {
            await Notification.createNotification({
              recipient: assignedUserId,
              sender: completedByUserId,
              type: "TASK_COMPLETED",
              title: "Task Completed",
              message: `${completedBy.name} completed the task: "${task.title}"`,
              priority: "LOW",
              relatedTask: task._id,
              metadata: {
                taskTitle: task.title,
                completedBy: completedBy.name,
                completedAt: new Date()
              },
              actions: [
                {
                  label: "View Task",
                  action: "VIEW_TASK",
                  style: "SECONDARY"
                }
              ]
            });
          }
        }
      }

      // Send email notifications
      await EmailService.sendTaskCompletionNotification(task, completedByUserId);

      console.log("Task completion notifications created");
    } catch (error) {
      console.error("Error creating task completion notifications:", error);
      throw error;
    }
  }

  /**
   * Create a task update notification
   */
  static async createTaskUpdateNotification(task, updatedByUserId, updateType = "updated") {
    try {
      const updatedBy = await User.findById(updatedByUserId).select("name");
      
      // Get all users who should be notified (creator + assigned users, excluding the updater)
      const usersToNotify = new Set();
      
      if (task.createdBy.toString() !== updatedByUserId.toString()) {
        usersToNotify.add(task.createdBy.toString());
      }
      
      if (task.assignedTo && task.assignedTo.length > 0) {
        task.assignedTo.forEach(userId => {
          if (userId.toString() !== updatedByUserId.toString()) {
            usersToNotify.add(userId.toString());
          }
        });
      }

      for (const userId of usersToNotify) {
        await Notification.createNotification({
          recipient: userId,
          sender: updatedByUserId,
          type: "TASK_UPDATED",
          title: "Task Updated",
          message: `${updatedBy.name} ${updateType} the task: "${task.title}"`,
          priority: "LOW",
          relatedTask: task._id,
          metadata: {
            taskTitle: task.title,
            updatedBy: updatedBy.name,
            updateType: updateType,
            updatedAt: new Date()
          },
          actions: [
            {
              label: "View Changes",
              action: "VIEW_TASK",
              style: "SECONDARY"
            }
          ]
        });
      }
      
      console.log(`Task update notifications created for ${usersToNotify.size} users`);
    } catch (error) {
      console.error("Error creating task update notifications:", error);
      throw error;
    }
  }

  /**
   * Create a friend request notification
   */
  static async createFriendRequestNotification(fromUserId, toUserId, requestId) {
    try {
      const fromUser = await User.findById(fromUserId).select("name");

      await Notification.createNotification({
        recipient: toUserId,
        sender: fromUserId,
        type: "FRIEND_REQUEST_SENT",
        title: "New Collaboration Request",
        message: `${fromUser.name} sent you a collaboration request`,
        priority: "MEDIUM",
        relatedUser: fromUserId,
        metadata: {
          fromUserName: fromUser.name,
          requestedAt: new Date(),
          requestId: requestId
        },
        actions: [
          {
            label: "Accept",
            action: "ACCEPT_FRIEND_REQUEST",
            style: "PRIMARY"
          },
          {
            label: "Decline",
            action: "DECLINE_FRIEND_REQUEST",
            style: "SECONDARY"
          }
        ]
      });

      // Send email notification
      await EmailService.sendFriendRequestNotification(fromUserId, toUserId);

      console.log("Friend request notification created");
    } catch (error) {
      console.error("Error creating friend request notification:", error);
      throw error;
    }
  }

  /**
   * Create a friend request accepted notification
   */
  static async createFriendRequestAcceptedNotification(fromUserId, toUserId) {
    try {
      const fromUser = await User.findById(fromUserId).select("name");

      await Notification.createNotification({
        recipient: toUserId,
        sender: fromUserId,
        type: "FRIEND_REQUEST_ACCEPTED",
        title: "Collaboration Request Accepted! 🎉",
        message: `${fromUser.name} accepted your collaboration request`,
        priority: "MEDIUM",
        relatedUser: fromUserId,
        metadata: {
          fromUserName: fromUser.name,
          acceptedAt: new Date()
        },
        actions: [
          {
            label: "View Profile",
            action: "VIEW_USER_PROFILE",
            style: "PRIMARY"
          }
        ]
      });

      console.log(`✅ Friend request accepted notification created for user ${toUserId}`);
    } catch (error) {
      console.error('❌ Error creating friend request accepted notification:', error);
    }
  }

  /**
   * Create a gamification notification (badge earned, level up, etc.)
   */
  static async createGamificationNotification(userId, type, data) {
    try {
      let title, message, priority = "LOW";
      
      switch (type) {
        case "BADGE_EARNED":
          title = "Badge Earned!";
          message = `Congratulations! You earned the "${data.badgeName}" badge`;
          priority = "MEDIUM";
          break;
        case "LEVEL_UP":
          title = "Level Up!";
          message = `Amazing! You've reached level ${data.newLevel}`;
          priority = "HIGH";
          break;
        case "XP_GAINED":
          title = "XP Gained";
          message = `You earned ${data.xpAmount} XP points!`;
          break;
        case "MISSION_COMPLETED":
          title = "Mission Completed!";
          message = `You completed the mission: "${data.missionTitle}"`;
          priority = "MEDIUM";
          break;
        default:
          title = "Achievement Unlocked";
          message = "You've made progress in your tasks!";
      }

      await Notification.createNotification({
        recipient: userId,
        sender: null, // System notification
        type: type,
        title: title,
        message: message,
        priority: priority,
        metadata: data,
        actions: [
          {
            label: "View Gallery",
            action: "VIEW_GALLERY",
            style: "PRIMARY"
          }
        ]
      });

      // Send email notification for badge earned
      if (type === "BADGE_EARNED") {
        await EmailService.sendBadgeEarnedNotification(userId, {
          name: data.badgeName,
          description: data.badgeDescription || `You earned the ${data.badgeName} badge!`
        });
      }

      console.log(`Gamification notification created: ${type}`);
    } catch (error) {
      console.error("Error creating gamification notification:", error);
      throw error;
    }
  }

  /**
   * Create a system announcement notification
   */
  static async createSystemNotification(userIds, title, message, priority = "MEDIUM") {
    try {
      for (const userId of userIds) {
        await Notification.createNotification({
          recipient: userId,
          sender: null, // System notification
          type: "SYSTEM_ANNOUNCEMENT",
          title: title,
          message: message,
          priority: priority,
          metadata: {
            isSystemNotification: true,
            createdAt: new Date()
          }
        });
      }
      
      console.log(`System notifications created for ${userIds.length} users`);
    } catch (error) {
      console.error("Error creating system notifications:", error);
      throw error;
    }
  }

  /**
   * Create overdue task notifications
   */
  static async createOverdueTaskNotifications() {
    try {
      const now = new Date();
      const overdueTasks = await Task.find({
        dueDate: { $lt: now },
        status: { $ne: "Completed" }
      }).populate("createdBy assignedTo", "name");

      for (const task of overdueTasks) {
        // Notify all assigned users
        if (task.assignedTo && task.assignedTo.length > 0) {
          for (const user of task.assignedTo) {
            await Notification.createNotification({
              recipient: user._id,
              sender: null, // System notification
              type: "TASK_OVERDUE",
              title: "Task Overdue",
              message: `The task "${task.title}" is overdue`,
              priority: "HIGH",
              relatedTask: task._id,
              metadata: {
                taskTitle: task.title,
                dueDate: task.dueDate,
                daysOverdue: Math.ceil((now - task.dueDate) / (1000 * 60 * 60 * 24))
              },
              actions: [
                {
                  label: "Complete Now",
                  action: "VIEW_TASK",
                  style: "PRIMARY"
                }
              ]
            });
          }
        }
      }
      
      console.log(`Overdue task notifications created for ${overdueTasks.length} tasks`);
    } catch (error) {
      console.error("Error creating overdue task notifications:", error);
      throw error;
    }
  }
}

export default NotificationService;
