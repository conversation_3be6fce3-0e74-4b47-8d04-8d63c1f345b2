import mongoose from "mongoose";

const FriendRequestSchema = new mongoose.Schema(
  {
    // User who sent the collaboration request
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },

    // User who received the collaboration request
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true
    },

    // Status of the collaboration request
    status: {
      type: String,
      enum: ["PENDING", "ACCEPTED", "DECLINED"],
      default: "PENDING",
      index: true
    },
    
    // Optional message with the collaboration request
    message: {
      type: String,
      maxlength: 200,
      default: ""
    },
    
    // When the request was responded to (accepted/declined)
    respondedAt: {
      type: Date,
      default: null
    },
    
    // Metadata for additional information
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },
  { 
    timestamps: true,
    // Compound index to prevent duplicate requests
    indexes: [
      { sender: 1, recipient: 1 }, // Unique combination
      { recipient: 1, status: 1 }, // For finding pending requests
      { sender: 1, status: 1 }     // For finding sent requests
    ]
  }
);

// Prevent duplicate collaboration requests between same users
FriendRequestSchema.index(
  { sender: 1, recipient: 1 }, 
  { 
    unique: true,
    partialFilterExpression: { status: "PENDING" }
  }
);

// Static methods for common operations
FriendRequestSchema.statics.findPendingRequests = function(userId) {
  return this.find({ 
    recipient: userId, 
    status: "PENDING" 
  }).populate('sender', 'name email profileImageUrl');
};

FriendRequestSchema.statics.findSentRequests = function(userId) {
  return this.find({ 
    sender: userId, 
    status: "PENDING" 
  }).populate('recipient', 'name email profileImageUrl');
};

FriendRequestSchema.statics.checkExistingRequest = function(senderId, recipientId) {
  return this.findOne({
    $or: [
      { sender: senderId, recipient: recipientId },
      { sender: recipientId, recipient: senderId }
    ],
    status: "PENDING"
  });
};

// Instance methods
FriendRequestSchema.methods.accept = async function() {
  this.status = "ACCEPTED";
  this.respondedAt = new Date();
  return await this.save();
};

FriendRequestSchema.methods.decline = async function() {
  this.status = "DECLINED";
  this.respondedAt = new Date();
  return await this.save();
};

const FriendRequest = mongoose.model("FriendRequest", FriendRequestSchema);

export default FriendRequest;
