import {
  LuLayoutDashboard,
  LuClipboardCheck,
  LuSquarePlus,
  LuLogOut,
  LuUsers,
  LuImage,
  LuTrendingUp,
  LuBrain,
  LuCalendar
} from "react-icons/lu";

export const SIDE_MENU_DATA  = [
  {
    id: "01",
    label: "Dashboard",
    icon: LuLayoutDashboard,
    path: "/user/dashboard",
  },
  {
    id: "02",
    label: "My Tasks",
    icon: LuClipboardCheck,
    path: "/user/tasks",
  },
  {
    id: "03",
    label: "Create Task",
    icon: LuSquarePlus,
    path: "/user/create-task",
  },
  {
    id: "04",
    label: "Team Members",
    icon: LuUsers,
    path: "/user/team",
  },
  {
    id: "05",
    label: "AI Smart Schedule",
    icon: LuBrain,
    path: "/user/ai-smart-schedule",
  },
  {
    id: "06",
    label: "Collaboration Ranking",
    icon: LuTrendingUp,
    path: "/user/collaboration-ranking",
  },
  {
    id: "07",
    label: "Gallery",              // ✅ New Menu Item
    icon: LuI<PERSON>,                 // ✅ Uses gallery/image icon
    path: "/user/gallery",        // ✅ This route must exist
  },
];

export const PRIORITY_DATA = [
  { label: "Low", value: "Low" },
  { label: "Medium", value: "Medium" },
  { label: "High", value: "High" },
];

export const STATUS_DATA = [
  { label: "Pending", value: "Pending" },
  { label: "In Progress", value: "In Progress" },
  { label: "Completed", value: "Completed" },
];
