import mongoose from "mongoose";

const todoSchema = new mongoose.Schema({
  text: { type: String, required: true },
  completed: { type: Boolean, default: false },
});

// Flexible attachment schema - supports both old string format and new object format
const attachmentSchema = new mongoose.Schema({
  name: { type: String },
  url: { type: String },
  size: { type: Number },
  type: { type: String },
  fileName: { type: String },
}, { _id: false, strict: false }); // Allow additional fields

const taskSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    description: { type: String },
    priority: { type: String, enum: ["Low", "Medium", "High"], default: "Medium" },
    status: { type: String, enum: ["Pending", "In Progress", "Completed"], default: "Pending" },
    dueDate: { type: Date, required: true },
    assignedTo: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],
    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    attachments: [mongoose.Schema.Types.Mixed], // Support both strings and objects
    todoChecklist: [todoSchema],
    progress: { type: Number, default: 0 },

    // Response time tracking fields for collaboration ranking
    assignedAt: { type: Date }, // When task was assigned to someone
    acceptedAt: { type: Date }, // When assignee acknowledged/accepted the task
    startedAt: { type: Date }, // When work actually began on the task
    completedAt: { type: Date }, // When task was marked as completed
    lastResponseAt: { type: Date }, // Last time assignee responded/updated
  },
  { timestamps: true }
);

// Pre-save middleware to handle attachment format
taskSchema.pre('save', function(next) {
  // Ensure attachments is always an array
  if (!Array.isArray(this.attachments)) {
    this.attachments = [];
  }

  // Convert any string attachments to object format for consistency
  this.attachments = this.attachments.map(attachment => {
    if (typeof attachment === 'string') {
      return {
        name: attachment,
        url: attachment,
        type: 'text/plain'
      };
    }
    return attachment;
  });

  next();
});

export default mongoose.model("Task", taskSchema);
