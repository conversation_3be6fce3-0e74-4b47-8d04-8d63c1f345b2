import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "../../components/layouts/DashboardLayout";
import axiosInstance from "../../utils/axiosInstance";
import { API_PATHS } from "../../utils/apiPaths";
import TaskStatusTabs from "../../components/TaskStatusTabs";
import TaskCard from "../../components/Cards/TaskCard";
import AppleCalendar from "../../components/Calendar/AppleCalendar";
import EnhancedAISuggestions from "../../components/AI/EnhancedAISuggestions";
import { LuFileSpreadsheet } from "react-icons/lu";
import toast from "react-hot-toast";

const MyTasks = () => {
  const [allTasks, setAllTasks] = useState([]);
  const [tabs, setTabs] = useState([]);
  const [filterStatus, setFilterStatus] = useState("All");
  const [viewMode, setViewMode] = useState("list");
  const [aiSuggestions, setAiSuggestions] = useState({});
  const [loadingTaskId, setLoadingTaskId] = useState(null);

  const navigate = useNavigate();

  const getAllTasks = async () => {
    try {
      const response = await axiosInstance.get(API_PATHS.TASKS.GET_ALL_TASKS, {
        params: {
          status: filterStatus === "All" ? "" : filterStatus,
        },
      });

      const tasks = response.data?.tasks || [];
      setAllTasks(tasks);

      const statusSummary = response.data?.statusSummary || {};
      const statusArray = [
        { label: "All", count: statusSummary.all || 0 },
        { label: "Pending", count: statusSummary.pendingTasks || 0 },
        { label: "In Progress", count: statusSummary.inProgressTasks || 0 },
        { label: "Completed", count: statusSummary.completedTasks || 0 },
      ];

      setTabs(statusArray);
    } catch (error) {
      console.error("Error fetching tasks:", error);
    }
  };

  const handleClick = (taskId) => {
    navigate(`/user/task-details/${taskId}`);
  };

  const handleCreateTaskForDate = (selectedDate) => {
    // Navigate to create task page with pre-filled due date
    navigate('/user/create-task', {
      state: {
        prefilledDueDate: selectedDate,
        fromCalendar: true
      }
    });
  };

  const handleDownloadReport = async () => {
    try {
      const response = await axiosInstance.get(API_PATHS.REPORTS.EXPORT_TASKS, {
        responseType: "blob",
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "task_details.xlsx");
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading details:", error);
    }
  };

  const getAiSuggestion = async (task) => {
    setLoadingTaskId(task._id);
    try {
      const response = await axiosInstance.post(API_PATHS.AI.PRIORITIZE, {
        title: task.title,
        description: task.description,
        priority: task.priority,
        dueDate: task.dueDate,
        assignedTo: task.assignedTo.map((u) => u.name),
        attachments: task.attachments,
        todoChecklist: task.todoChecklist,
      });

      const result = response.data;

      setAiSuggestions((prev) => ({
        ...prev,
        [task._id]: {
          ...prev[task._id],
          priority: result.priority,
          suggestedDates: result.suggestedDates || [],
        },
      }));
    } catch (err) {
      console.error("AI suggestion failed:", err);
      alert("Failed to generate AI prioritization.");
    } finally {
      setLoadingTaskId(null);
    }
  };

  const getExplanation = async (task) => {
    try {
      const response = await axiosInstance.post(API_PATHS.AI.EXPLAIN, {
        title: task.title,
        description: task.description,
        priority: aiSuggestions[task._id]?.priority || task.priority,
        dueDate: task.dueDate,
        assignedTo: task.assignedTo.map((u) => u.name),
        attachments: task.attachments,
        todoChecklist: task.todoChecklist,
      });

      const result = response.data;

      setAiSuggestions((prev) => ({
        ...prev,
        [task._id]: {
          ...prev[task._id],
          explanation: result.explanation,
        },
      }));
    } catch (err) {
      console.error("AI explanation failed:", err);
      alert("Failed to generate AI explanation.");
    }
  };

  // Enhanced AI Functions
  const handleApplyAIPriority = async (taskId, newPriority) => {
    try {
      const response = await axiosInstance.put(API_PATHS.TASKS.UPDATE_TASK(taskId), {
        priority: newPriority
      });

      if (response.status === 200) {
        toast.success(`Task priority updated to ${newPriority}!`);

        // Update local task state
        setAllTasks(prev => prev.map(task =>
          task._id === taskId ? { ...task, priority: newPriority } : task
        ));

        // Update AI suggestions to reflect the change
        setAiSuggestions(prev => ({
          ...prev,
          [taskId]: {
            ...prev[taskId],
            userPriority: newPriority,
            priorityMatch: 'ALIGNED'
          }
        }));
      }
    } catch (error) {
      console.error("Failed to update task priority:", error);
      toast.error("Failed to update task priority");
    }
  };

  const handleScheduleTask = async (taskId, suggestedDate) => {
    try {
      // Convert DD/MM/YYYY to YYYY-MM-DD format
      const [day, month, year] = suggestedDate.split('/');
      const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

      const response = await axiosInstance.put(API_PATHS.TASKS.UPDATE_TASK(taskId), {
        dueDate: new Date(formattedDate).toISOString()
      });

      if (response.status === 200) {
        toast.success(`Task scheduled for ${suggestedDate}!`);

        // Update local task state
        setAllTasks(prev => prev.map(task =>
          task._id === taskId ? { ...task, dueDate: new Date(formattedDate).toISOString() } : task
        ));
      }
    } catch (error) {
      console.error("Failed to schedule task:", error);
      toast.error("Failed to schedule task");
    }
  };

  useEffect(() => {
    getAllTasks();
  }, [filterStatus]);

  return (
    <DashboardLayout activeMenu="My Tasks">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
        {/* Apple-style Header */}
        <div className="sticky top-0 z-10 bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
              <div>
                <h1 className="text-3xl font-semibold text-gray-900 leading-tight">
                  My Tasks
                </h1>
                <p className="text-gray-600 mt-1">
                  Manage and organize your tasks efficiently
                </p>
              </div>

              <div className="flex items-center gap-4">
                {/* Apple-style View Toggle */}
                <div className="flex items-center bg-gray-100/80 backdrop-blur-sm p-1.5 rounded-2xl shadow-sm">
                  <button
                    onClick={() => setViewMode("list")}
                    className={`flex items-center gap-2 px-4 py-2.5 rounded-xl transition-all duration-300 ease-out ${
                      viewMode === "list"
                        ? "bg-white text-blue-600 shadow-lg shadow-blue-600/20 scale-105"
                        : "text-gray-600 hover:bg-white/50"
                    }`}
                  >
                    📋 <span className="hidden sm:inline font-medium">List</span>
                  </button>
                  <button
                    onClick={() => setViewMode("calendar")}
                    className={`flex items-center gap-2 px-4 py-2.5 rounded-xl transition-all duration-300 ease-out ${
                      viewMode === "calendar"
                        ? "bg-white text-blue-600 shadow-lg shadow-blue-600/20 scale-105"
                        : "text-gray-600 hover:bg-white/50"
                    }`}
                  >
                    📅 <span className="hidden sm:inline font-medium">Calendar</span>
                  </button>
                </div>

                {/* Apple-style Download Button */}
                <button
                  className="inline-flex items-center gap-2 px-4 py-2.5 text-sm font-semibold text-green-700 bg-green-100/80 hover:bg-green-200/80 rounded-xl shadow-lg shadow-green-600/10 hover:shadow-green-600/20 transition-all duration-300 ease-out hover:scale-105 backdrop-blur-sm"
                  onClick={handleDownloadReport}
                >
                  <LuFileSpreadsheet className="w-4 h-4" />
                  <span className="hidden sm:inline">Download Report</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Content Container */}
        <div className="max-w-7xl mx-auto px-6 py-8">

          {/* Apple-style Status Tabs */}
          {tabs.length > 0 && (
            <div className="mb-8">
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-lg shadow-gray-200/50 border border-gray-200/30 p-2">
                <div className="flex flex-wrap gap-2">
                  {tabs.map((tab) => (
                    <button
                      key={tab.label}
                      onClick={() => setFilterStatus(tab.label)}
                      className={`flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-300 ease-out ${
                        filterStatus === tab.label
                          ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25 scale-105"
                          : "text-gray-700 hover:bg-gray-100/80 hover:scale-102"
                      }`}
                    >
                      <span className="font-medium">{tab.label}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                        filterStatus === tab.label
                          ? "bg-white/20 text-white"
                          : "bg-gray-200 text-gray-600"
                      }`}>
                        {tab.count}
                      </span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Apple-style Content */}
          {viewMode === "list" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {allTasks?.length > 0 ? (
                allTasks.map((item) => (
                  <div key={item._id} className="group">
                    <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-lg shadow-gray-200/50 border border-gray-200/30 overflow-hidden hover:shadow-xl hover:shadow-gray-200/60 transition-all duration-500 ease-out hover:scale-[1.02]">
                      <TaskCard
                        title={item.title}
                        description={item.description}
                        priority={item.priority}
                        status={item.status}
                        progress={item.progress}
                        createdAt={item.createdAt}
                        dueDate={item.dueDate}
                        assignedTo={item.assignedTo?.map((a) => a.profileImageUrl)}
                        attachmentCount={item.attachments?.length || 0}
                        completedTodoCount={item.completedTodoCount || 0}
                        todoChecklist={item.todoChecklist || []}
                        onClick={() => handleClick(item._id)}
                      />

                      {/* Apple-style AI Suggestions */}
                      <div className="p-4 border-t border-gray-100/50">
                        <button
                          className="inline-flex items-center gap-2 px-3 py-2 text-xs font-semibold text-purple-700 bg-purple-100/80 hover:bg-purple-200/80 rounded-xl transition-all duration-300 ease-out hover:scale-105"
                          onClick={() => getAiSuggestion(item)}
                          disabled={loadingTaskId === item._id}
                        >
                          {loadingTaskId === item._id ? (
                            <>
                              <div className="w-3 h-3 border border-purple-600 border-t-transparent rounded-full animate-spin"></div>
                              Generating...
                            </>
                          ) : (
                            <>
                              🤖 AI Prioritization
                            </>
                          )}
                        </button>

                        {/* Enhanced AI Suggestions */}
                        <EnhancedAISuggestions
                          suggestion={aiSuggestions[item._id]}
                          onApplyPriority={(newPriority) => handleApplyAIPriority(item._id, newPriority)}
                          onScheduleTask={(suggestedDate) => handleScheduleTask(item._id, suggestedDate)}
                        />

                        {/* Show Explanation Button if not already shown */}
                        {aiSuggestions[item._id] && !aiSuggestions[item._id].explanation && (
                          <div className="mt-3">
                            <button
                              onClick={() => getExplanation(item)}
                              className="inline-flex items-center gap-1 px-3 py-1.5 text-xs font-semibold text-indigo-700 bg-indigo-100 hover:bg-indigo-200 rounded-xl transition-all duration-300 ease-out hover:scale-105"
                            >
                              💬 Show Detailed Explanation
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                </div>
              ))
              ) : (
                <div className="col-span-full flex flex-col items-center justify-center py-16">
                  <div className="w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mb-6">
                    <span className="text-4xl">📋</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    No tasks found
                  </h3>
                  <p className="text-gray-600 text-center max-w-md">
                    No tasks found for <strong>{filterStatus}</strong> status.
                    {filterStatus !== "All" && " Try switching to a different status or create a new task."}
                  </p>
                </div>
              )}
            </div>
          ) : (
            /* Apple-style Calendar View */
            <div className="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-gray-200/30 overflow-hidden min-h-[600px]">
              <AppleCalendar
                tasks={allTasks}
                onTaskClick={handleClick}
                onDateClick={(date) => console.log('Selected date:', date)}
                onTaskUpdate={() => {
                  // Refresh tasks after drag and drop
                  getAllTasks();
                }}
                onCreateTask={handleCreateTaskForDate}
              />
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default MyTasks;
