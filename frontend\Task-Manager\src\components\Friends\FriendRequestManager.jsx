import React, { useState, useEffect } from 'react';
import axiosInstance from '../../utils/axiosInstance';
import { toast } from 'react-toastify';
import './FriendRequestManager.css';

const FriendRequestManager = ({ onFriendshipChange, refreshTrigger }) => {
  const [receivedRequests, setReceivedRequests] = useState([]);
  const [sentRequests, setSentRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('received');

  useEffect(() => {
    fetchFriendRequests();
  }, []);

  // Refresh when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger) {
      fetchFriendRequests();
    }
  }, [refreshTrigger]);

  const fetchFriendRequests = async () => {
    try {
      setLoading(true);
      const [receivedRes, sentRes] = await Promise.all([
        axiosInstance.get('/api/friend-requests/received'),
        axiosInstance.get('/api/friend-requests/sent')
      ]);
      
      setReceivedRequests(receivedRes.data);
      setSentRequests(sentRes.data);
    } catch (error) {
      console.error('Error fetching collaboration requests:', error);
      toast.error('Failed to load collaboration requests');
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptRequest = async (requestId) => {
    try {
      await axiosInstance.post(`/api/friend-requests/accept/${requestId}`);
      toast.success('Collaboration request accepted! 🎉');

      // Refresh requests and notify parent component
      await fetchFriendRequests();
      if (onFriendshipChange) onFriendshipChange();
    } catch (error) {
      console.error('Error accepting collaboration request:', error);
      toast.error('Failed to accept collaboration request');
    }
  };

  const handleDeclineRequest = async (requestId) => {
    try {
      await axiosInstance.post(`/api/friend-requests/decline/${requestId}`);
      toast.success('Collaboration request declined');

      // Refresh requests
      await fetchFriendRequests();
    } catch (error) {
      console.error('Error declining collaboration request:', error);
      toast.error('Failed to decline collaboration request');
    }
  };

  const handleCancelRequest = async (requestId) => {
    try {
      await axiosInstance.delete(`/api/friend-requests/cancel/${requestId}`);
      toast.success('Collaboration request cancelled');

      // Refresh requests
      await fetchFriendRequests();
    } catch (error) {
      console.error('Error cancelling collaboration request:', error);
      toast.error('Failed to cancel collaboration request');
    }
  };

  if (loading) {
    return (
      <div className="friend-request-manager">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading collaboration requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="friend-request-manager">
      <div className="request-tabs">
        <button 
          className={`tab-button ${activeTab === 'received' ? 'active' : ''}`}
          onClick={() => setActiveTab('received')}
        >
          Received ({receivedRequests.length})
        </button>
        <button 
          className={`tab-button ${activeTab === 'sent' ? 'active' : ''}`}
          onClick={() => setActiveTab('sent')}
        >
          Sent ({sentRequests.length})
        </button>
      </div>

      <div className="request-content">
        {activeTab === 'received' && (
          <div className="received-requests">
            <h3>📥 Collaboration Requests Received</h3>
            {receivedRequests.length === 0 ? (
              <div className="no-requests">
                <p>No pending collaboration requests</p>
              </div>
            ) : (
              <div className="request-list">
                {receivedRequests.map((request) => (
                  <div key={request._id} className="request-card">
                    <div className="request-info">
                      <div className="user-avatar">
                        {request.sender.profileImageUrl ? (
                          <img src={request.sender.profileImageUrl} alt={request.sender.name} />
                        ) : (
                          <div className="avatar-placeholder">
                            {request.sender.name.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="request-details">
                        <h4>{request.sender.name}</h4>
                        <p className="email">{request.sender.email}</p>
                        {request.message && (
                          <p className="message">"{request.message}"</p>
                        )}
                        <p className="timestamp">
                          {new Date(request.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="request-actions">
                      <button 
                        className="accept-btn"
                        onClick={() => handleAcceptRequest(request._id)}
                      >
                        ✅ Accept
                      </button>
                      <button 
                        className="decline-btn"
                        onClick={() => handleDeclineRequest(request._id)}
                      >
                        ❌ Decline
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'sent' && (
          <div className="sent-requests">
            <h3>📤 Collaboration Requests Sent</h3>
            {sentRequests.length === 0 ? (
              <div className="no-requests">
                <p>No pending sent requests</p>
              </div>
            ) : (
              <div className="request-list">
                {sentRequests.map((request) => (
                  <div key={request._id} className="request-card">
                    <div className="request-info">
                      <div className="user-avatar">
                        {request.recipient.profileImageUrl ? (
                          <img src={request.recipient.profileImageUrl} alt={request.recipient.name} />
                        ) : (
                          <div className="avatar-placeholder">
                            {request.recipient.name.charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="request-details">
                        <h4>{request.recipient.name}</h4>
                        <p className="email">{request.recipient.email}</p>
                        {request.message && (
                          <p className="message">"{request.message}"</p>
                        )}
                        <p className="timestamp">
                          Sent on {new Date(request.createdAt).toLocaleDateString()}
                        </p>
                        <span className="status-badge pending">⏳ Pending</span>
                      </div>
                    </div>
                    <div className="request-actions">
                      <button 
                        className="cancel-btn"
                        onClick={() => handleCancelRequest(request._id)}
                      >
                        🚫 Cancel
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FriendRequestManager;
