import FriendRequest from "../models/FriendRequest.js";
import User from "../models/User.js";
import NotificationService from "../utils/notificationService.js";
import EmailService from "../services/emailService.js";
import { checkAndCompleteFirstFriendMission } from "./userController.js";

// @desc    Send a collaboration request
// @route   POST /api/friend-requests/send/:userId
// @access  Private
const sendFriendRequest = async (req, res) => {
  try {
    const { userId: recipientId } = req.params;
    const senderId = req.user._id;
    const { message = "" } = req.body;



    // Validate recipient exists
    const recipient = await User.findById(recipientId);
    if (!recipient) {

      return res.status(404).json({ message: "User not found" });
    }

    // Can't send request to yourself
    if (senderId.toString() === recipientId) {

      return res.status(400).json({ message: "You cannot send a friend request to yourself" });
    }

    // Check if they're already friends
    const sender = await User.findById(senderId);
    if (sender.friends.includes(recipientId)) {

      return res.status(400).json({ message: "You are already friends with this user" });
    }

    // Check for existing pending request (either direction)
    const existingRequest = await FriendRequest.checkExistingRequest(senderId, recipientId);
    if (existingRequest) {

      return res.status(400).json({
        message: "A collaboration request already exists between you and this user"
      });
    }

    // Create friend request
    const friendRequest = await FriendRequest.create({
      sender: senderId,
      recipient: recipientId,
      message: message.trim(),
      status: "PENDING"
    });

    // Create notification for recipient
    await NotificationService.createFriendRequestNotification(senderId, recipientId, friendRequest._id);

    // Send email notification
    try {
      await EmailService.sendFriendRequestNotification(senderId, recipientId);
    } catch (emailError) {
      console.error('❌ Failed to send friend request email:', emailError);
      // Don't fail the request if email fails
    }

    // Populate sender info for response
    await friendRequest.populate('sender', 'name email profileImageUrl');

    console.log('✅ Collaboration request sent successfully:', friendRequest._id);

    res.status(201).json({
      message: "Collaboration request sent successfully",
      friendRequest
    });

  } catch (error) {
    console.error('❌ Error sending collaboration request:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Accept a collaboration request
// @route   POST /api/friend-requests/accept/:requestId
// @access  Private
const acceptFriendRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const userId = req.user._id;

    // Find the friend request
    const friendRequest = await FriendRequest.findById(requestId)
      .populate('sender', 'name email profileImageUrl')
      .populate('recipient', 'name email profileImageUrl');

    if (!friendRequest) {
      return res.status(404).json({ message: "Collaboration request not found" });
    }

    // Verify the current user is the recipient
    if (friendRequest.recipient._id.toString() !== userId.toString()) {
      return res.status(403).json({ message: "You can only accept requests sent to you" });
    }

    // Check if request is still pending
    if (friendRequest.status !== "PENDING") {
      return res.status(400).json({ message: "This collaboration request has already been responded to" });
    }

    // Accept the request
    await friendRequest.accept();

    // Add each other as friends
    const sender = await User.findById(friendRequest.sender._id);
    const recipient = await User.findById(friendRequest.recipient._id);

    // Add to friends lists (if not already there)
    if (!sender.friends.includes(recipient._id)) {
      sender.friends.push(recipient._id);
      await sender.save();
    }
    
    if (!recipient.friends.includes(sender._id)) {
      recipient.friends.push(sender._id);
      await recipient.save();
    }

    // Check if this is the first friend for either user (for mission completion)
    if (sender.friends.length === 1) {
      // This was sender's first friend - complete mission
      await checkAndCompleteFirstFriendMission(sender._id);
    }
    
    if (recipient.friends.length === 1) {
      // This was recipient's first friend - complete mission
      await checkAndCompleteFirstFriendMission(recipient._id);
    }

    // Create notification for sender (request accepted)
    await NotificationService.createFriendRequestAcceptedNotification(
      recipient._id, 
      sender._id
    );

    // Send email notification to sender
    try {
      await EmailService.sendFriendRequestAcceptedNotification(recipient._id, sender._id);
    } catch (emailError) {
      console.error('❌ Failed to send friend request accepted email:', emailError);
    }

    res.status(200).json({
      message: "Collaboration request accepted successfully",
      friendRequest
    });

  } catch (error) {
    console.error('❌ Error accepting collaboration request:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Decline a collaboration request
// @route   POST /api/friend-requests/decline/:requestId
// @access  Private
const declineFriendRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const userId = req.user._id;

    // Find the friend request
    const friendRequest = await FriendRequest.findById(requestId)
      .populate('sender', 'name email profileImageUrl')
      .populate('recipient', 'name email profileImageUrl');

    if (!friendRequest) {
      return res.status(404).json({ message: "Collaboration request not found" });
    }

    // Verify the current user is the recipient
    if (friendRequest.recipient._id.toString() !== userId.toString()) {
      return res.status(403).json({ message: "You can only decline requests sent to you" });
    }

    // Check if request is still pending
    if (friendRequest.status !== "PENDING") {
      return res.status(400).json({ message: "This collaboration request has already been responded to" });
    }

    // Decline the request
    await friendRequest.decline();

    res.status(200).json({
      message: "Collaboration request declined successfully",
      friendRequest
    });

  } catch (error) {
    console.error('❌ Error declining collaboration request:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get pending collaboration requests (received)
// @route   GET /api/friend-requests/received
// @access  Private
const getReceivedFriendRequests = async (req, res) => {
  try {
    const userId = req.user._id;
    
    const requests = await FriendRequest.findPendingRequests(userId);
    
    res.status(200).json(requests);
  } catch (error) {
    console.error('❌ Error getting received collaboration requests:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get sent collaboration requests
// @route   GET /api/friend-requests/sent
// @access  Private
const getSentFriendRequests = async (req, res) => {
  try {
    const userId = req.user._id;
    
    const requests = await FriendRequest.findSentRequests(userId);
    
    res.status(200).json(requests);
  } catch (error) {
    console.error('❌ Error getting sent collaboration requests:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Cancel a sent collaboration request
// @route   DELETE /api/friend-requests/cancel/:requestId
// @access  Private
const cancelFriendRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const userId = req.user._id;

    const friendRequest = await FriendRequest.findById(requestId);
    
    if (!friendRequest) {
      return res.status(404).json({ message: "Collaboration request not found" });
    }

    // Verify the current user is the sender
    if (friendRequest.sender.toString() !== userId.toString()) {
      return res.status(403).json({ message: "You can only cancel requests you sent" });
    }

    // Can only cancel pending requests
    if (friendRequest.status !== "PENDING") {
      return res.status(400).json({ message: "You can only cancel pending requests" });
    }

    await FriendRequest.findByIdAndDelete(requestId);

    res.status(200).json({ message: "Collaboration request cancelled successfully" });
  } catch (error) {
    console.error('❌ Error cancelling collaboration request:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};



export {
  sendFriendRequest,
  acceptFriendRequest,
  declineFriendRequest,
  getReceivedFriendRequests,
  getSentFriendRequests,
  cancelFriendRequest
};
