import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Comprehensive Attachment Analysis Service
 * Analyzes PDFs, images, videos, and documents for AI-powered task scheduling
 */
class AttachmentAnalyzer {
  constructor() {
    this.supportedTypes = {
      pdf: ['application/pdf'],
      image: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      video: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'],
      document: ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    };
  }

  /**
   * Main analysis function - processes all attachments for a task
   */
  async analyzeTaskAttachments(attachments) {
    if (!attachments || attachments.length === 0) {
      return {
        totalComplexity: 'Low',
        totalProcessingTime: 0,
        attachmentAnalysis: [],
        schedulingRecommendations: this.getBasicSchedulingRecommendations()
      };
    }

    const analysisPromises = attachments.map(attachment => this.analyzeAttachment(attachment));
    const attachmentAnalysis = await Promise.all(analysisPromises);

    return {
      totalComplexity: this.calculateOverallComplexity(attachmentAnalysis),
      totalProcessingTime: this.calculateTotalProcessingTime(attachmentAnalysis),
      attachmentAnalysis,
      schedulingRecommendations: this.generateSchedulingRecommendations(attachmentAnalysis)
    };
  }

  /**
   * Analyze individual attachment
   */
  async analyzeAttachment(attachment) {
    const fileType = this.determineFileType(attachment.mimetype);
    const fileSize = this.getFileSize(attachment.filename);

    switch (fileType) {
      case 'pdf':
        return await this.analyzePDF(attachment, fileSize);
      case 'image':
        return await this.analyzeImage(attachment, fileSize);
      case 'video':
        return await this.analyzeVideo(attachment, fileSize);
      case 'document':
        return await this.analyzeDocument(attachment, fileSize);
      default:
        return this.analyzeGenericFile(attachment, fileSize);
    }
  }

  /**
   * PDF Analysis
   */
  async analyzePDF(attachment, fileSize) {
    // Estimate complexity based on file size and name patterns
    const complexity = this.assessPDFComplexity(attachment.filename, fileSize);
    const estimatedPages = Math.ceil(fileSize / 50000); // Rough estimate: 50KB per page
    const readingTime = Math.ceil(estimatedPages * 2); // 2 minutes per page

    return {
      type: 'PDF',
      filename: attachment.filename,
      fileSize,
      complexity,
      estimatedPages,
      processingTime: readingTime,
      cognitiveLoad: complexity === 'High' ? 'High' : 'Medium',
      optimalTimeSlot: 'Morning (9:00-11:00)',
      insights: `${estimatedPages}-page document requiring focused reading`,
      requirements: ['PDF reader', 'Note-taking tools'],
      schedulingPriority: complexity === 'High' ? 1 : 2
    };
  }

  /**
   * Image Analysis
   */
  async analyzeImage(attachment, fileSize) {
    const complexity = this.assessImageComplexity(attachment.filename, fileSize);
    const processingTime = complexity === 'High' ? 15 : complexity === 'Medium' ? 8 : 3;

    return {
      type: 'Image',
      filename: attachment.filename,
      fileSize,
      complexity,
      processingTime,
      cognitiveLoad: 'Medium',
      optimalTimeSlot: 'Morning (10:00-12:00)',
      insights: this.generateImageInsights(attachment.filename),
      requirements: ['Image viewer', 'Analysis tools'],
      schedulingPriority: 3
    };
  }

  /**
   * Video Analysis
   */
  async analyzeVideo(attachment, fileSize) {
    const estimatedDuration = Math.ceil(fileSize / 1000000); // Rough estimate: 1MB per minute
    const complexity = this.assessVideoComplexity(attachment.filename, fileSize);
    const totalTime = Math.ceil(estimatedDuration * 1.3); // 30% extra for note-taking

    return {
      type: 'Video',
      filename: attachment.filename,
      fileSize,
      complexity,
      estimatedDuration,
      processingTime: totalTime,
      cognitiveLoad: 'High',
      optimalTimeSlot: 'Afternoon (2:00-4:00)',
      insights: `${estimatedDuration}-minute video requiring active watching`,
      requirements: ['Video player', 'Note-taking setup'],
      schedulingPriority: 2,
      suggestedBreaks: Math.ceil(estimatedDuration / 25) // Break every 25 minutes
    };
  }

  /**
   * Document Analysis
   */
  async analyzeDocument(attachment, fileSize) {
    const complexity = this.assessDocumentComplexity(attachment.filename, fileSize);
    const estimatedPages = Math.ceil(fileSize / 30000); // Rough estimate for Word docs
    const readingTime = Math.ceil(estimatedPages * 1.5); // 1.5 minutes per page

    return {
      type: 'Document',
      filename: attachment.filename,
      fileSize,
      complexity,
      estimatedPages,
      processingTime: readingTime,
      cognitiveLoad: 'Medium',
      optimalTimeSlot: 'Morning (9:00-11:00)',
      insights: `${estimatedPages}-page document for review`,
      requirements: ['Word processor', 'Editing tools'],
      schedulingPriority: 2
    };
  }

  /**
   * Generic File Analysis
   */
  analyzeGenericFile(attachment, fileSize) {
    return {
      type: 'File',
      filename: attachment.filename,
      fileSize,
      complexity: 'Low',
      processingTime: 5,
      cognitiveLoad: 'Low',
      optimalTimeSlot: 'Flexible',
      insights: 'Standard file processing',
      requirements: ['Appropriate software'],
      schedulingPriority: 4
    };
  }

  /**
   * Helper Methods
   */
  determineFileType(mimetype) {
    for (const [type, mimetypes] of Object.entries(this.supportedTypes)) {
      if (mimetypes.includes(mimetype)) {
        return type;
      }
    }
    return 'generic';
  }

  getFileSize(filename) {
    try {
      const uploadsPath = path.join(__dirname, '../uploads', filename);
      if (fs.existsSync(uploadsPath)) {
        const stats = fs.statSync(uploadsPath);
        return stats.size;
      }
    } catch (error) {
      console.log('File size estimation error:', error.message);
    }
    return 100000; // Default size estimate
  }

  assessPDFComplexity(filename, fileSize) {
    const name = filename.toLowerCase();
    if (name.includes('research') || name.includes('thesis') || name.includes('academic') || fileSize > 5000000) {
      return 'High';
    }
    if (name.includes('report') || name.includes('analysis') || fileSize > 1000000) {
      return 'Medium';
    }
    return 'Low';
  }

  assessImageComplexity(filename, fileSize) {
    const name = filename.toLowerCase();
    if (name.includes('chart') || name.includes('diagram') || name.includes('graph') || fileSize > 2000000) {
      return 'High';
    }
    if (name.includes('screenshot') || name.includes('figure') || fileSize > 500000) {
      return 'Medium';
    }
    return 'Low';
  }

  assessVideoComplexity(filename, fileSize) {
    const name = filename.toLowerCase();
    if (name.includes('lecture') || name.includes('tutorial') || name.includes('presentation') || fileSize > 100000000) {
      return 'High';
    }
    if (name.includes('demo') || name.includes('explanation') || fileSize > 50000000) {
      return 'Medium';
    }
    return 'Low';
  }

  assessDocumentComplexity(filename, fileSize) {
    const name = filename.toLowerCase();
    if (name.includes('specification') || name.includes('requirements') || fileSize > 1000000) {
      return 'High';
    }
    if (name.includes('guide') || name.includes('manual') || fileSize > 500000) {
      return 'Medium';
    }
    return 'Low';
  }

  generateImageInsights(filename) {
    const name = filename.toLowerCase();
    if (name.includes('chart') || name.includes('graph')) {
      return 'Data visualization requiring analysis';
    }
    if (name.includes('diagram') || name.includes('flowchart')) {
      return 'Process diagram needing interpretation';
    }
    if (name.includes('screenshot')) {
      return 'Interface screenshot for reference';
    }
    return 'Visual content for review';
  }

  calculateOverallComplexity(analysisArray) {
    const complexityScores = analysisArray.map(analysis => {
      switch (analysis.complexity) {
        case 'High': return 3;
        case 'Medium': return 2;
        case 'Low': return 1;
        default: return 1;
      }
    });

    const averageScore = complexityScores.reduce((sum, score) => sum + score, 0) / complexityScores.length;
    
    if (averageScore >= 2.5) return 'High';
    if (averageScore >= 1.5) return 'Medium';
    return 'Low';
  }

  calculateTotalProcessingTime(analysisArray) {
    return analysisArray.reduce((total, analysis) => total + analysis.processingTime, 0);
  }

  generateSchedulingRecommendations(analysisArray) {
    if (analysisArray.length === 0) {
      return this.getBasicSchedulingRecommendations();
    }

    // Sort by scheduling priority
    const sortedAttachments = [...analysisArray].sort((a, b) => a.schedulingPriority - b.schedulingPriority);
    
    return {
      optimalOrder: sortedAttachments.map(a => a.filename),
      totalEstimatedTime: this.calculateTotalProcessingTime(analysisArray),
      suggestedBreaks: Math.ceil(this.calculateTotalProcessingTime(analysisArray) / 45), // Break every 45 minutes
      cognitiveLoadDistribution: this.distributeCognitiveLoad(sortedAttachments),
      timeSlotRecommendations: this.generateTimeSlotRecommendations(sortedAttachments)
    };
  }

  getBasicSchedulingRecommendations() {
    return {
      optimalOrder: [],
      totalEstimatedTime: 30, // Default 30 minutes for task without attachments
      suggestedBreaks: 0,
      cognitiveLoadDistribution: 'Even',
      timeSlotRecommendations: ['Flexible timing']
    };
  }

  distributeCognitiveLoad(attachments) {
    const highLoad = attachments.filter(a => a.cognitiveLoad === 'High').length;
    const mediumLoad = attachments.filter(a => a.cognitiveLoad === 'Medium').length;
    
    if (highLoad > 2) return 'Heavy - Schedule across multiple days';
    if (highLoad > 0 || mediumLoad > 3) return 'Moderate - Include breaks';
    return 'Light - Can be completed in one session';
  }

  generateTimeSlotRecommendations(attachments) {
    const recommendations = [];
    
    attachments.forEach(attachment => {
      recommendations.push(`${attachment.filename}: ${attachment.optimalTimeSlot}`);
    });
    
    return recommendations;
  }
}

export default AttachmentAnalyzer;
