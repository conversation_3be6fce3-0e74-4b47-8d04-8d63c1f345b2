/* AI Smart Schedule Styles */
.ai-schedule-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-schedule-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-schedule-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

.ai-schedule-header p {
  margin: 0 0 20px 0;
  color: #7f8c8d;
  font-size: 1.1rem;
  text-align: center;
  line-height: 1.6;
}

.schedule-legend {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #34495e;
  font-weight: 500;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 2px solid rgba(0, 0, 0, 0.1);
}

.legend-color.work {
  background-color: #4CAF50;
}

.legend-color.attachment {
  background-color: #FF9800;
}

.legend-color.break {
  background-color: #9C27B0;
}

/* Calendar Wrapper */
.calendar-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom Toolbar */
.ai-calendar-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 2px solid #ecf0f1;
  margin-bottom: 20px;
}

.toolbar-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.calendar-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 700;
}

.toolbar-views {
  display: flex;
  gap: 10px;
}

.view-button {
  background: transparent;
  color: #7f8c8d;
  border: 2px solid #ecf0f1;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.view-button:hover {
  border-color: #667eea;
  color: #667eea;
}

.view-button.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Calendar Overrides */
.rbc-calendar {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.rbc-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  font-weight: 600;
  padding: 15px 10px;
  border-bottom: 2px solid #dee2e6;
}

.rbc-today {
  background-color: rgba(102, 126, 234, 0.1);
}

.rbc-event {
  border-radius: 8px !important;
  border: none !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
}

.rbc-event:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  font-size: 1.2rem;
  font-weight: 500;
}

/* Task Details Modal */
.task-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 2px solid #ecf0f1;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px 20px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
}

.close-button {
  background: none;
  border: none;
  font-size: 2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.modal-body {
  padding: 30px;
}

.work-details, .attachment-details {
  line-height: 1.8;
}

.work-details p, .attachment-details p {
  margin: 0 0 15px 0;
  color: #34495e;
}

.work-details strong, .attachment-details strong {
  color: #2c3e50;
  font-weight: 600;
}

.ai-insights {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 15px;
  margin-top: 20px;
}

.ai-insights h4 {
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 700;
}

.ai-insights p {
  margin: 0 0 10px 0;
  color: rgba(255, 255, 255, 0.9);
}

.requirements {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin-top: 20px;
}

.requirements h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.requirements ul {
  margin: 0;
  padding-left: 20px;
}

.requirements li {
  margin-bottom: 8px;
  color: #34495e;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-schedule-container {
    padding: 10px;
  }
  
  .ai-schedule-header {
    padding: 20px;
  }
  
  .ai-schedule-header h1 {
    font-size: 2rem;
  }
  
  .schedule-legend {
    gap: 15px;
  }
  
  .ai-calendar-toolbar {
    flex-direction: column;
    gap: 15px;
  }
  
  .toolbar-navigation {
    flex-direction: column;
    gap: 10px;
  }
  
  .calendar-title {
    font-size: 1.4rem;
  }
  
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .modal-header, .modal-body {
    padding: 20px;
  }
}
