/* Apple-Style AI Smart Schedule */
.ai-schedule-container {
  padding: 0;
  background: #f5f5f7;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Helvetica, Arial, sans-serif;
  color: #1d1d1f;
}

.ai-schedule-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 0;
  padding: 40px 24px;
  margin: 0;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.ai-schedule-header h1 {
  margin: 0 0 8px 0;
  color: #1d1d1f;
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  letter-spacing: -0.003em;
  line-height: 1.125;
}

.ai-schedule-header p {
  margin: 0 0 24px 0;
  color: #86868b;
  font-size: 17px;
  text-align: center;
  line-height: 1.47059;
  font-weight: 400;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.schedule-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1d1d1f;
  font-weight: 400;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 20px;
  transition: all 0.2s ease;
}

.legend-item:hover {
  background: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.legend-color.work {
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
}

.legend-color.attachment {
  background: linear-gradient(135deg, #ff9500 0%, #ff9f0a 100%);
}

.legend-color.break {
  background: linear-gradient(135deg, #af52de 0%, #bf5af2 100%);
}

/* Calendar Wrapper */
.calendar-wrapper {
  background: #ffffff;
  border-radius: 12px;
  margin: 24px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 0.5px solid rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

/* Apple-Style Custom Toolbar */
.ai-calendar-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.toolbar-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-button {
  background: #007aff;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 80px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover {
  background: #0056cc;
  transform: translateY(-0.5px);
}

.nav-button:active {
  background: #004499;
  transform: translateY(0);
}

.calendar-title {
  margin: 0;
  color: #1d1d1f;
  font-size: 22px;
  font-weight: 600;
  letter-spacing: -0.022em;
}

.toolbar-views {
  display: flex;
  gap: 0;
  background: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  padding: 2px;
}

.view-button {
  background: transparent;
  color: #1d1d1f;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 60px;
  height: 32px;
}

.view-button:hover {
  background: rgba(0, 0, 0, 0.06);
}

.view-button.active {
  background: #ffffff;
  color: #1d1d1f;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Apple-Style Calendar Overrides */
.rbc-calendar {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Helvetica, Arial, sans-serif;
  background: #ffffff;
  padding: 0 24px 24px 24px;
}

.rbc-header {
  background: #f5f5f7;
  color: #1d1d1f;
  font-weight: 500;
  font-size: 14px;
  padding: 12px 8px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: -0.008em;
}

.rbc-today {
  background-color: rgba(0, 122, 255, 0.08);
}

.rbc-event {
  border-radius: 6px !important;
  border: none !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
}

.rbc-event:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.rbc-event:focus {
  outline: 2px solid #007aff;
  outline-offset: 2px;
}

/* Apple-Style Event Colors */
.rbc-event.work-session {
  background: linear-gradient(135deg, #34c759 0%, #30d158 100%) !important;
  color: white !important;
}

.rbc-event.attachment-processing {
  background: linear-gradient(135deg, #ff9500 0%, #ff9f0a 100%) !important;
  color: white !important;
}

.rbc-event.break-time {
  background: linear-gradient(135deg, #af52de 0%, #bf5af2 100%) !important;
  color: white !important;
}

/* Apple-Style Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: #ffffff;
  border-radius: 12px;
  margin: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 0.5px solid rgba(0, 0, 0, 0.04);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #86868b;
  font-size: 17px;
  font-weight: 400;
  text-align: center;
  line-height: 1.47059;
}

/* Apple-Style Task Details Modal */
.task-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.modal-content {
  background: #ffffff;
  border-radius: 16px;
  max-width: 560px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border: 0.5px solid rgba(0, 0, 0, 0.04);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  background: #ffffff;
}

.modal-header h3 {
  margin: 0;
  color: #1d1d1f;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: -0.022em;
}

.close-button {
  background: rgba(0, 0, 0, 0.04);
  border: none;
  font-size: 18px;
  color: #86868b;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.08);
  color: #1d1d1f;
}

.close-button:active {
  background: rgba(0, 0, 0, 0.12);
  transform: scale(0.95);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(80vh - 120px);
}

.work-details, .attachment-details {
  line-height: 1.47059;
}

.work-details p, .attachment-details p {
  margin: 0 0 12px 0;
  color: #1d1d1f;
  font-size: 15px;
  font-weight: 400;
}

.work-details strong, .attachment-details strong {
  color: #1d1d1f;
  font-weight: 500;
}

.ai-insights {
  background: #f5f5f7;
  color: #1d1d1f;
  padding: 16px;
  border-radius: 12px;
  margin-top: 16px;
  border: 0.5px solid rgba(0, 0, 0, 0.04);
}

.ai-insights h4 {
  margin: 0 0 8px 0;
  font-size: 17px;
  font-weight: 600;
  color: #1d1d1f;
  letter-spacing: -0.022em;
}

.ai-insights p {
  margin: 0 0 8px 0;
  line-height: 1.47059;
  font-size: 15px;
  color: #1d1d1f;
}

.requirements {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 15px;
  margin-top: 20px;
}

.requirements h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  font-weight: 600;
}

.requirements ul {
  margin: 0;
  padding-left: 20px;
}

.requirements li {
  margin-bottom: 8px;
  color: #34495e;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-schedule-container {
    padding: 10px;
  }
  
  .ai-schedule-header {
    padding: 20px;
  }
  
  .ai-schedule-header h1 {
    font-size: 2rem;
  }
  
  .schedule-legend {
    gap: 15px;
  }
  
  .ai-calendar-toolbar {
    flex-direction: column;
    gap: 15px;
  }
  
  .toolbar-navigation {
    flex-direction: column;
    gap: 10px;
  }
  
  .calendar-title {
    font-size: 1.4rem;
  }
  
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .modal-header, .modal-body {
    padding: 20px;
  }
}

/* Apple-Style Calendar Enhancements */
.rbc-date-cell {
  padding: 8px 4px;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: #1d1d1f;
}

.rbc-off-range-bg {
  background: #f5f5f7;
}

.rbc-current-time-indicator {
  background-color: #ff3b30;
  height: 2px;
  z-index: 3;
  border-radius: 1px;
}

.rbc-month-view {
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.rbc-month-row {
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-day-bg {
  border-right: 0.5px solid rgba(0, 0, 0, 0.1);
}

.rbc-day-bg:last-child {
  border-right: none;
}

.rbc-time-view {
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.rbc-time-header {
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
}

.rbc-time-content {
  border-top: none;
}

.rbc-timeslot-group {
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
}

.rbc-time-slot {
  border-top: 0.5px solid rgba(0, 0, 0, 0.05);
}
