import React from 'react';
import PersonalSubtaskItem from './PersonalSubtaskItem';
import EnhancedSubtaskItem from './EnhancedSubtaskItem';

const SmartSubtaskItem = ({ 
  subtask, 
  subtaskIndex, 
  taskId, 
  onUpdate, 
  currentUser,
  task
}) => {
  // Determine if this is a collaborative task
  const isCollaborative = () => {
    // Debug logging
    console.log('🔍 Task Collaboration Detection:', {
      taskId: task._id,
      taskTitle: task.title,
      assignedTo: task.assignedTo,
      sharedWith: task.sharedWith,
      createdBy: task.createdBy,
      currentUser: currentUser?._id
    });

    // Check if task has assignedTo (other than creator) or shared users
    const hasAssignees = task.assignedTo && task.assignedTo.length > 0;
    const hasSharedUsers = task.sharedWith && task.sharedWith.length > 0;
    const hasMultipleParticipants = hasAssignees || hasSharedUsers;

    console.log('📊 Collaboration Analysis:', {
      hasAssignees,
      hasSharedUsers,
      hasMultipleParticipants,
      assigneeCount: task.assignedTo?.length || 0,
      sharedUserCount: task.sharedWith?.length || 0
    });

    // If task creator is the only participant, it's personal
    if (!hasMultipleParticipants) {
      console.log('✅ Detected as PERSONAL task');
      return false;
    }

    // If there are multiple participants, it's collaborative
    console.log('🤝 Detected as COLLABORATIVE task');
    return true;
  };

  const collaborative = isCollaborative();

  // For personal tasks, use simple checkbox interface
  if (!collaborative) {
    return (
      <PersonalSubtaskItem
        subtask={subtask}
        subtaskIndex={subtaskIndex}
        taskId={taskId}
        onUpdate={onUpdate}
        task={task}
      />
    );
  }

  // For collaborative tasks, use enhanced tracking interface
  return (
    <EnhancedSubtaskItem
      subtask={subtask}
      subtaskIndex={subtaskIndex}
      taskId={taskId}
      onUpdate={onUpdate}
      currentUser={currentUser}
      taskAssignees={task.assignedTo || []}
      isCollaborative={true}
    />
  );
};

export default SmartSubtaskItem;
