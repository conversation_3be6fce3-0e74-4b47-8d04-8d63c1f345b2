import React, { useEffect, useState, useContext } from "react";
import DashboardLayout from "../../components/layouts/DashboardLayout";
import axiosInstance from "../../utils/axiosInstance";
import { UserContext } from "../../context/userContext";
import { toast } from "react-toastify";
import {
  LuTrendingUp,
  LuStar,
  LuClock,
  LuUserX,
  LuMessageCircle,
  LuFilter,
  LuRefreshCw,
  LuBarChart,
  LuUsers,
  LuAward,
  LuShield,
  LuZap
} from "react-icons/lu";

const CollaborationRanking = () => {
  const { user: contextUser } = useContext(UserContext);
  const [currentUser, setCurrentUser] = useState(null);
  const [friends, setFriends] = useState([]);
  const [userTasks, setUserTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [rankingFilter, setRankingFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  // Fetch data on component mount
  useEffect(() => {
    fetchAllData();
  }, [contextUser]);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      let user = contextUser;
      if (!user) {
        try {
          const profileRes = await axiosInstance.get("/api/auth/profile");
          user = profileRes.data;
        } catch (error) {
          // Fallback user
          user = { _id: "686899e58df03c0034242553", name: "Luqman" };
        }
      }
      setCurrentUser(user);

      // Fetch friends
      const friendsRes = await axiosInstance.get("/api/users/friends");
      setFriends(friendsRes.data);

      // Fetch user tasks
      const tasksRes = await axiosInstance.get("/api/tasks");
      const allTasks = Array.isArray(tasksRes.data) ? tasksRes.data : 
                     tasksRes.data?.tasks || tasksRes.data?.data || [];
      setUserTasks(allTasks);

    } catch (error) {
      console.error("Error fetching collaboration data:", error);
      toast.error("Failed to load collaboration data");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAllData();
    setRefreshing(false);
    toast.success("Collaboration data refreshed!");
  };

  // ===== COLLABORATION RANKING FUNCTIONS =====
  
  // Calculate response time in hours
  const calculateResponseTime = (assignedAt, respondedAt) => {
    if (!assignedAt || !respondedAt) return null;
    const assigned = new Date(assignedAt);
    const responded = new Date(respondedAt);
    return (responded - assigned) / (1000 * 60 * 60);
  };

  // Format response time for display
  const formatResponseTime = (hours) => {
    if (hours === null || hours === undefined) return "No data";
    if (hours < 1) return `${Math.round(hours * 60)} min`;
    if (hours < 24) return `${Math.round(hours)} hrs`;
    return `${Math.round(hours / 24)} days`;
  };

  // Get response time category with color and icon
  const getResponseTimeCategory = (averageHours) => {
    if (averageHours === null || averageHours === undefined) 
      return { category: "NO_DATA", color: "gray", icon: "❓" };
    if (averageHours <= 2) return { category: "INSTANT", color: "green", icon: "⚡" };
    if (averageHours <= 8) return { category: "FAST", color: "blue", icon: "🚀" };
    if (averageHours <= 24) return { category: "NORMAL", color: "yellow", icon: "⏰" };
    if (averageHours <= 72) return { category: "SLOW", color: "orange", icon: "🐌" };
    return { category: "VERY_SLOW", color: "red", icon: "🚨" };
  };

  // Calculate collaboration metrics for a specific friend
  const calculateCollaborationMetrics = (friend) => {
    if (!userTasks || !currentUser) return null;

    // Tasks assigned TO the friend by current user
    const tasksAssignedToFriend = userTasks.filter(task => 
      task.assignedTo && task.assignedTo.some(assignee => 
        (typeof assignee === 'string' ? assignee : assignee._id) === friend._id
      ) && task.createdBy === currentUser._id
    );

    // Tasks assigned BY the friend to current user (placeholder)
    const tasksFromFriend = [];

    // Calculate metrics
    const totalTasksGiven = tasksAssignedToFriend.length;
    const totalTasksReceived = tasksFromFriend.length;
    const completedTasksByFriend = tasksAssignedToFriend.filter(task => task.status === 'Completed').length;
    const completionRate = totalTasksGiven > 0 ? (completedTasksByFriend / totalTasksGiven) * 100 : 0;

    // Calculate reciprocity ratio
    const reciprocityRatio = totalTasksGiven > 0 ? totalTasksReceived / totalTasksGiven : 0;

    // Calculate average response time
    const tasksWithResponseTime = tasksAssignedToFriend.filter(task => task.assignedAt && task.acceptedAt);
    const totalResponseTime = tasksWithResponseTime.reduce((sum, task) => {
      const responseTime = calculateResponseTime(task.assignedAt, task.acceptedAt);
      return sum + (responseTime || 0);
    }, 0);
    const averageResponseTime = tasksWithResponseTime.length > 0 ? totalResponseTime / tasksWithResponseTime.length : null;

    // Determine free rider risk
    let freeRiderRisk = 'LOW';
    if (reciprocityRatio < 0.3 || completionRate < 50) freeRiderRisk = 'HIGH';
    else if (reciprocityRatio < 0.6 || completionRate < 75) freeRiderRisk = 'MEDIUM';

    // Calculate overall collaboration score
    const collaborationScore = Math.round(
      (reciprocityRatio * 30) +
      (completionRate * 0.4) +
      ((averageResponseTime ? Math.max(0, 100 - averageResponseTime) : 50) * 0.2) +
      (Math.min(totalTasksGiven + totalTasksReceived, 50) * 0.1)
    );

    return {
      userId: friend._id,
      name: friend.name,
      avatar: friend.avatar,
      collaborationScore,
      reciprocityRatio,
      completionRate,
      averageResponseTime,
      totalCollaborations: totalTasksGiven + totalTasksReceived,
      tasksGiven: totalTasksGiven,
      tasksReceived: totalTasksReceived,
      freeRiderRisk,
      trend: 'STABLE'
    };
  };

  // Calculate collaboration rankings for all friends
  const calculateCollaborationRankings = () => {
    if (!friends || friends.length === 0) return [];

    const rankings = friends
      .map(friend => calculateCollaborationMetrics(friend))
      .filter(metrics => metrics !== null)
      .sort((a, b) => b.collaborationScore - a.collaborationScore)
      .map((collab, index) => ({ ...collab, rank: index + 1 }));

    return rankings;
  };

  // Filter rankings based on current filter
  const getFilteredRankings = () => {
    const rankings = calculateCollaborationRankings();
    
    switch (rankingFilter) {
      case 'top':
        return rankings.filter(r => r.collaborationScore >= 70);
      case 'risk':
        return rankings.filter(r => r.freeRiderRisk === 'HIGH' || r.freeRiderRisk === 'MEDIUM');
      default:
        return rankings;
    }
  };

  const handleFriendMessage = (friend) => {
    toast.info(`Opening conversation with ${friend.name}`);
    // TODO: Implement messaging functionality
  };

  const handleLimitCollaboration = (friend) => {
    if (window.confirm(`Are you sure you want to limit collaboration with ${friend.name}? This will reduce their task assignment priority.`)) {
      toast.warning(`Collaboration limited with ${friend.name}`);
      // TODO: Implement collaboration limiting
    }
  };

  if (loading) {
    return (
      <DashboardLayout activeMenu="Collaboration Ranking">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 font-medium">Loading collaboration insights...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout activeMenu="Collaboration Ranking">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">
                  🏆 Collaboration Rankings
                </h1>
                <p className="text-gray-600 text-lg">
                  Discover your best collaborators and identify collaboration patterns
                </p>
              </div>
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-200 disabled:opacity-50"
              >
                <LuRefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
                {refreshing ? 'Refreshing...' : 'Refresh Data'}
              </button>
            </div>

            {/* Filter Tabs */}
            <div className="flex bg-gray-100 rounded-xl p-1 w-fit">
              <button
                onClick={() => setRankingFilter("all")}
                className={`px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2 ${
                  rankingFilter === "all"
                    ? "bg-white text-blue-600 shadow-md"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <LuUsers size={16} />
                All ({friends.length})
              </button>
              <button
                onClick={() => setRankingFilter("top")}
                className={`px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2 ${
                  rankingFilter === "top"
                    ? "bg-white text-blue-600 shadow-md"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <LuAward size={16} />
                Top Performers
              </button>
              <button
                onClick={() => setRankingFilter("risk")}
                className={`px-6 py-3 rounded-lg transition-all duration-200 flex items-center gap-2 ${
                  rankingFilter === "risk"
                    ? "bg-white text-red-600 shadow-md"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <LuShield size={16} />
                Risk Assessment
              </button>
            </div>
          </div>

          {/* Main Content */}
          {friends.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <LuUsers className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Collaborators Yet</h3>
              <p className="text-gray-600 mb-6">Start collaborating by adding friends and assigning tasks!</p>
              <button className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors">
                Add Collaborators
              </button>
            </div>
          ) : (
            <>
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <LuUsers className="w-6 h-6 text-blue-600" />
                    </div>
                    <span className="text-2xl font-bold text-gray-900">{friends.length}</span>
                  </div>
                  <h3 className="font-semibold text-gray-900">Total Collaborators</h3>
                  <p className="text-sm text-gray-600">Active team members</p>
                </div>

                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                      <LuAward className="w-6 h-6 text-green-600" />
                    </div>
                    <span className="text-2xl font-bold text-gray-900">
                      {getFilteredRankings().filter(r => r.collaborationScore >= 70).length}
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900">Top Performers</h3>
                  <p className="text-sm text-gray-600">Score ≥ 70</p>
                </div>

                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                      <LuZap className="w-6 h-6 text-yellow-600" />
                    </div>
                    <span className="text-2xl font-bold text-gray-900">
                      {getFilteredRankings().filter(r => r.averageResponseTime && r.averageResponseTime <= 8).length}
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900">Fast Responders</h3>
                  <p className="text-sm text-gray-600">≤ 8 hours</p>
                </div>

                <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                      <LuShield className="w-6 h-6 text-red-600" />
                    </div>
                    <span className="text-2xl font-bold text-gray-900">
                      {getFilteredRankings().filter(r => r.freeRiderRisk === 'HIGH').length}
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900">High Risk</h3>
                  <p className="text-sm text-gray-600">Needs attention</p>
                </div>
              </div>

              {/* Rankings Table */}
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-100">
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                    <LuBarChart className="w-5 h-5" />
                    Collaboration Rankings
                  </h2>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Rank</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Collaborator</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Score</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Reciprocity</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Completion</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Response Time</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Risk Level</th>
                        <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {getFilteredRankings().map((collab) => {
                        const responseCategory = getResponseTimeCategory(collab.averageResponseTime);
                        return (
                          <tr key={collab.userId} className="hover:bg-gray-50 transition-colors">
                            <td className="px-6 py-4">
                              <div className="flex items-center">
                                {collab.rank <= 3 ? (
                                  <span className="text-2xl">
                                    {collab.rank === 1 ? "🥇" : collab.rank === 2 ? "🥈" : "🥉"}
                                  </span>
                                ) : (
                                  <span className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-semibold text-gray-600">
                                    {collab.rank}
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                  {collab.name.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900">{collab.name}</div>
                                  <div className="text-sm text-gray-600">
                                    {collab.totalCollaborations} total interactions
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-center">
                              <div className="flex flex-col items-center">
                                <span className="text-2xl font-bold text-gray-900">{collab.collaborationScore}</span>
                                <div className="w-16 bg-gray-200 rounded-full h-2 mt-1">
                                  <div
                                    className={`h-2 rounded-full ${
                                      collab.collaborationScore >= 80 ? 'bg-green-500' :
                                      collab.collaborationScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${collab.collaborationScore}%` }}
                                  ></div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-center">
                              <div className="flex flex-col items-center">
                                <span className="font-semibold text-gray-900">
                                  {(collab.reciprocityRatio * 100).toFixed(0)}%
                                </span>
                                <div className="w-12 bg-gray-200 rounded-full h-1.5 mt-1">
                                  <div
                                    className={`h-1.5 rounded-full ${
                                      collab.reciprocityRatio >= 0.6 ? 'bg-green-500' :
                                      collab.reciprocityRatio >= 0.3 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${collab.reciprocityRatio * 100}%` }}
                                  ></div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-center">
                              <div className="flex flex-col items-center">
                                <span className="font-semibold text-gray-900">
                                  {collab.completionRate.toFixed(0)}%
                                </span>
                                <div className="w-12 bg-gray-200 rounded-full h-1.5 mt-1">
                                  <div
                                    className={`h-1.5 rounded-full ${
                                      collab.completionRate >= 75 ? 'bg-green-500' :
                                      collab.completionRate >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${collab.completionRate}%` }}
                                  ></div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-center">
                              <div className="flex flex-col items-center">
                                <span className="text-lg">{responseCategory.icon}</span>
                                <span className="text-sm font-medium text-gray-900">
                                  {formatResponseTime(collab.averageResponseTime)}
                                </span>
                                <span className={`text-xs px-2 py-1 rounded-full mt-1 ${
                                  responseCategory.color === 'green' ? 'bg-green-100 text-green-800' :
                                  responseCategory.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                                  responseCategory.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                                  responseCategory.color === 'orange' ? 'bg-orange-100 text-orange-800' :
                                  responseCategory.color === 'red' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {responseCategory.category}
                                </span>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-center">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                collab.freeRiderRisk === 'LOW' ? 'bg-green-100 text-green-800' :
                                collab.freeRiderRisk === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {collab.freeRiderRisk}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <div className="flex items-center justify-center gap-2">
                                <button
                                  onClick={() => handleFriendMessage(collab)}
                                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                  title="Send Message"
                                >
                                  <LuMessageCircle size={16} />
                                </button>
                                {collab.freeRiderRisk === 'HIGH' && (
                                  <button
                                    onClick={() => handleLimitCollaboration(collab)}
                                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                    title="Limit Collaboration"
                                  >
                                    <LuUserX size={16} />
                                  </button>
                                )}
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>

                {getFilteredRankings().length === 0 && (
                  <div className="text-center py-12">
                    <LuFilter className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Results Found</h3>
                    <p className="text-gray-600">Try adjusting your filter criteria</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CollaborationRanking;
