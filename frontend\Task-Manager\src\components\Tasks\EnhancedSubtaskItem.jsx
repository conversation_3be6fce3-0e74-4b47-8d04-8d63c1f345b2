import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ser,
  LuMessageCircle,
  LuEdit,
  LuUserPlus
} from 'react-icons/lu';
import axiosInstance from '../../utils/axiosInstance';
import { toast } from 'react-toastify';

const EnhancedSubtaskItem = ({ 
  subtask, 
  subtaskIndex, 
  taskId, 
  onUpdate, 
  currentUser,
  taskAssignees = []
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [comment, setComment] = useState('');
  const [estimatedHours, setEstimatedHours] = useState(subtask.estimatedHours || 1);
  const [actualHours, setActualHours] = useState(subtask.actualHours || 0);
  const [showAssignModal, setShowAssignModal] = useState(false);

  const updateSubtaskStatus = async (newStatus) => {
    setIsUpdating(true);
    try {
      const response = await axiosInstance.put(
        `/api/tasks/${taskId}/subtasks/${subtaskIndex}/status`,
        {
          status: newStatus,
          estimatedHours: newStatus === 'in_progress' ? estimatedHours : undefined,
          actualHours: newStatus === 'completed' ? actualHours : undefined,
          comment: comment.trim() || undefined
        }
      );
      
      if (response.status === 200) {
        onUpdate(response.data.task);
        setComment('');
        toast.success(`Subtask ${newStatus.replace('_', ' ')}`);
      }
    } catch (error) {
      toast.error(`Failed to update subtask: ${error.response?.data?.message || error.message}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const assignSubtask = async (assignedUserId) => {
    try {
      const response = await axiosInstance.put(
        `/api/tasks/${taskId}/subtasks/${subtaskIndex}/assign`,
        { assignedTo: assignedUserId }
      );
      
      if (response.status === 200) {
        onUpdate(response.data.task);
        setShowAssignModal(false);
        toast.success('Subtask assigned successfully');
      }
    } catch (error) {
      toast.error(`Failed to assign subtask: ${error.response?.data?.message || error.message}`);
    }
  };

  const getStatusColor = () => {
    switch (subtask.status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'in_progress': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    switch (subtask.status) {
      case 'completed': return <LuCheck className="w-4 h-4" />;
      case 'in_progress': return <LuPlay className="w-4 h-4" />;
      default: return <LuClock className="w-4 h-4" />;
    }
  };

  const canUserModify = () => {
    return !subtask.assignedTo || 
           subtask.assignedTo._id === currentUser._id || 
           currentUser._id === taskId; // Task creator can always modify
  };

  return (
    <div className="border border-gray-200 rounded-xl p-4 hover:shadow-sm transition-all duration-200">
      {/* Main Subtask Row */}
      <div className="flex items-center gap-3">
        {/* Status Indicator */}
        <div className={`px-2 py-1 rounded-lg border text-xs font-medium flex items-center gap-1 ${getStatusColor()}`}>
          {getStatusIcon()}
          {subtask.status.replace('_', ' ')}
        </div>

        {/* Subtask Text */}
        <div className="flex-1">
          <span className={`font-medium ${subtask.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
            {subtask.text}
          </span>
          
          {/* Assignment Info */}
          {subtask.assignedTo && (
            <div className="flex items-center gap-1 mt-1 text-xs text-gray-600">
              <LuUser className="w-3 h-3" />
              Assigned to {subtask.assignedTo.name}
            </div>
          )}
        </div>

        {/* Time Tracking */}
        {(subtask.estimatedHours || subtask.actualHours) && (
          <div className="text-xs text-gray-600 flex items-center gap-1">
            <LuClock className="w-3 h-3" />
            {subtask.actualHours ? `${subtask.actualHours}h` : `~${subtask.estimatedHours}h`}
          </div>
        )}

        {/* Action Buttons */}
        {canUserModify() && (
          <div className="flex items-center gap-1">
            {subtask.status === 'pending' && (
              <button
                onClick={() => updateSubtaskStatus('in_progress')}
                disabled={isUpdating}
                className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Start working"
              >
                <LuPlay className="w-4 h-4" />
              </button>
            )}
            
            {subtask.status === 'in_progress' && (
              <button
                onClick={() => updateSubtaskStatus('completed')}
                disabled={isUpdating}
                className="p-1.5 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Mark complete"
              >
                <LuCheck className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={() => setShowAssignModal(true)}
              className="p-1.5 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
              title="Assign to user"
            >
              <LuUserPlus className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="p-1.5 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
              title="Show details"
            >
              <LuEdit className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Expanded Details */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-100 space-y-3">
          {/* Time Estimation */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Estimated Hours
              </label>
              <input
                type="number"
                value={estimatedHours}
                onChange={(e) => setEstimatedHours(Number(e.target.value))}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="0.5"
                step="0.5"
              />
            </div>
            
            {subtask.status === 'completed' && (
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  Actual Hours
                </label>
                <input
                  type="number"
                  value={actualHours}
                  onChange={(e) => setActualHours(Number(e.target.value))}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  min="0.1"
                  step="0.1"
                />
              </div>
            )}
          </div>

          {/* Comment */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Add Comment
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add a comment about this subtask..."
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows="2"
            />
          </div>

          {/* Completion History */}
          {subtask.completedBy && (
            <div className="bg-green-50 rounded-lg p-3">
              <div className="flex items-center gap-2 text-sm text-green-800">
                <LuCheck className="w-4 h-4" />
                <span>Completed by {subtask.completedBy.name}</span>
                {subtask.completedAt && (
                  <span className="text-green-600">
                    on {new Date(subtask.completedAt).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Comments History */}
          {subtask.comments && subtask.comments.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-gray-700">Comments:</h4>
              {subtask.comments.map((comment, idx) => (
                <div key={idx} className="bg-gray-50 rounded-lg p-2">
                  <div className="flex items-center gap-2 text-xs text-gray-600 mb-1">
                    <LuMessageCircle className="w-3 h-3" />
                    <span>{comment.user.name}</span>
                    <span>{new Date(comment.timestamp).toLocaleDateString()}</span>
                  </div>
                  <p className="text-sm text-gray-800">{comment.text}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Assignment Modal */}
      {showAssignModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Assign Subtask
            </h3>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {taskAssignees.map((user) => (
                <button
                  key={user._id}
                  onClick={() => assignSubtask(user._id)}
                  className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg transition-colors text-left"
                >
                  <img
                    src={user.profileImageUrl || '/default-user.png'}
                    alt={user.name}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <span className="font-medium text-gray-900">{user.name}</span>
                </button>
              ))}
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={() => setShowAssignModal(false)}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedSubtaskItem;
