import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  LuUserPlus
} from 'react-icons/lu';
import axiosInstance from '../../utils/axiosInstance';
import { toast } from 'react-toastify';

const EnhancedSubtaskItem = ({
  subtask,
  subtaskIndex,
  taskId,
  onUpdate,
  currentUser,
  taskAssignees = [],
  isCollaborative = false
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showAssignModal, setShowAssignModal] = useState(false);

  const updateSubtaskStatus = async (newStatus) => {
    setIsUpdating(true);
    try {
      const response = await axiosInstance.put(
        `/api/tasks/${taskId}/subtasks/${subtaskIndex}/status`,
        {
          status: newStatus,
          // Only send basic status update - no complex time tracking
          estimatedHours: subtask.estimatedHours || 1,
          actualHours: subtask.actualHours || 1
        }
      );

      if (response.status === 200) {
        onUpdate(response.data.task);

        // Better user feedback
        const statusMessages = {
          'completed': '✅ Subtask completed!',
          'pending': '↩️ Subtask marked as incomplete',
          'in_progress': '🔄 Subtask in progress'
        };

        toast.success(statusMessages[newStatus] || `Subtask ${newStatus.replace('_', ' ')}`);
      }
    } catch (error) {
      console.error('Subtask update error:', error);
      toast.error(`Failed to update subtask: ${error.response?.data?.message || error.message}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const assignSubtask = async (assignedUserId) => {
    try {
      setShowAssignModal(false); // Close modal immediately for better UX

      const response = await axiosInstance.put(
        `/api/tasks/${taskId}/subtasks/${subtaskIndex}/assign`,
        { assignedTo: assignedUserId }
      );

      if (response.status === 200) {
        onUpdate(response.data.task);

        // Find assigned user name for better feedback
        const assignedUser = taskAssignees.find(user => user._id === assignedUserId);
        toast.success(`📋 Subtask assigned to ${assignedUser?.name || 'team member'}`);
      }
    } catch (error) {
      console.error('Assignment error:', error);
      toast.error(`Failed to assign subtask: ${error.response?.data?.message || error.message}`);
      setShowAssignModal(true); // Reopen modal if assignment failed
    }
  };

  const getStatusColor = () => {
    const status = subtask.status || 'pending';
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'in_progress': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    const status = subtask.status || 'pending';
    switch (status) {
      case 'completed': return <LuCheck className="w-4 h-4" />;
      case 'in_progress': return <LuPlay className="w-4 h-4" />;
      default: return <LuClock className="w-4 h-4" />;
    }
  };

  const canUserModify = () => {
    if (!currentUser) return false;
    return !subtask.assignedTo ||
           subtask.assignedTo._id === currentUser._id ||
           currentUser._id === taskId; // Task creator can always modify
  };

  return (
    <div className="border border-gray-200 rounded-xl p-4 hover:shadow-sm transition-all duration-200">
      {/* Main Subtask Row */}
      <div className="flex items-center gap-3">
        {/* Status Indicator */}
        <div className={`px-2 py-1 rounded-lg border text-xs font-medium flex items-center gap-1 ${getStatusColor()}`}>
          {getStatusIcon()}
          {(subtask.status || 'pending').replace('_', ' ')}
        </div>

        {/* Subtask Text */}
        <div className="flex-1">
          <span className={`font-medium ${subtask.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
            {subtask.text}
          </span>
          
          {/* Enhanced Assignment & Completion Info */}
          <div className="flex items-center gap-2 mt-1 text-xs">
            {subtask.assignedTo && (
              <div className="flex items-center gap-1 text-blue-600 bg-blue-50 px-2 py-1 rounded-md">
                <LuUser className="w-3 h-3" />
                {subtask.assignedTo.name}
              </div>
            )}

            {subtask.completed && subtask.completedBy && (
              <div className="flex items-center gap-1 text-green-600 bg-green-50 px-2 py-1 rounded-md">
                <LuCheck className="w-3 h-3" />
                Completed by {subtask.completedBy.name}
                {subtask.completedAt && (
                  <span className="text-green-500 ml-1">
                    • {new Date(subtask.completedAt).toLocaleDateString()}
                  </span>
                )}
              </div>
            )}

            {!subtask.assignedTo && !subtask.completed && (
              <div className="flex items-center gap-1 text-gray-400">
                <LuUser className="w-3 h-3" />
                Unassigned
              </div>
            )}
          </div>
        </div>

        {/* Time Tracking */}
        {(subtask.estimatedHours || subtask.actualHours) && (
          <div className="text-xs text-gray-600 flex items-center gap-1">
            <LuClock className="w-3 h-3" />
            {subtask.actualHours ? `${subtask.actualHours}h` : `~${subtask.estimatedHours}h`}
          </div>
        )}

        {/* Improved Action Buttons */}
        {canUserModify() && (
          <div className="flex items-center gap-2">
            {/* Status-aware completion button */}
            {!subtask.completed ? (
              <button
                onClick={() => updateSubtaskStatus('completed')}
                disabled={isUpdating}
                className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg transition-all border ${
                  isUpdating
                    ? 'text-gray-400 bg-gray-50 border-gray-200 cursor-not-allowed'
                    : 'text-green-600 hover:bg-green-50 border-green-200 hover:border-green-300'
                }`}
                title="Mark as completed"
              >
                {isUpdating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <LuCheck className="w-4 h-4" />
                    Complete
                  </>
                )}
              </button>
            ) : (
              <button
                onClick={() => updateSubtaskStatus('pending')}
                disabled={isUpdating}
                className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-lg transition-all border ${
                  isUpdating
                    ? 'text-gray-400 bg-gray-50 border-gray-200 cursor-not-allowed'
                    : 'text-gray-600 hover:bg-gray-50 border-gray-200 hover:border-gray-300'
                }`}
                title="Mark as incomplete"
              >
                {isUpdating ? 'Updating...' : 'Undo'}
              </button>
            )}

            {/* Assignment button with better visibility */}
            <button
              onClick={() => setShowAssignModal(true)}
              className="flex items-center gap-1 px-2 py-1.5 text-xs text-gray-500 hover:bg-gray-50 rounded-lg transition-colors border border-gray-200"
              title={subtask.assignedTo ? `Reassign from ${subtask.assignedTo.name}` : 'Assign to someone'}
            >
              <LuUserPlus className="w-3 h-3" />
              {subtask.assignedTo ? 'Reassign' : 'Assign'}
            </button>
          </div>
        )}
      </div>

      {/* Simple Assignment Modal */}
      {showAssignModal && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900">Assign to:</h4>
            <button
              onClick={() => setShowAssignModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="space-y-2 max-h-32 overflow-y-auto">
            {taskAssignees.map((assignee) => (
              <button
                key={assignee._id}
                onClick={() => assignSubtask(assignee._id)}
                className="w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 rounded-lg transition-colors"
              >
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                  {assignee.name.charAt(0).toUpperCase()}
                </div>
                <span className="text-sm text-gray-900">{assignee.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedSubtaskItem;
