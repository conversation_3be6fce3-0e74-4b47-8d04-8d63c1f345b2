import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  LuUserPlus
} from 'react-icons/lu';
import axiosInstance from '../../utils/axiosInstance';
import { toast } from 'react-toastify';

const EnhancedSubtaskItem = ({
  subtask,
  subtaskIndex,
  taskId,
  onUpdate,
  currentUser,
  taskAssignees = [],
  isCollaborative = false
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showAssignModal, setShowAssignModal] = useState(false);

  const updateSubtaskStatus = async (newStatus) => {
    setIsUpdating(true);
    try {
      const response = await axiosInstance.put(
        `/api/tasks/${taskId}/subtasks/${subtaskIndex}/status`,
        {
          status: newStatus,
          estimatedHours: newStatus === 'in_progress' ? estimatedHours : undefined,
          actualHours: newStatus === 'completed' ? actualHours : undefined,
          comment: comment.trim() || undefined
        }
      );
      
      if (response.status === 200) {
        onUpdate(response.data.task);
        setComment('');
        toast.success(`Subtask ${newStatus.replace('_', ' ')}`);
      }
    } catch (error) {
      toast.error(`Failed to update subtask: ${error.response?.data?.message || error.message}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const assignSubtask = async (assignedUserId) => {
    try {
      const response = await axiosInstance.put(
        `/api/tasks/${taskId}/subtasks/${subtaskIndex}/assign`,
        { assignedTo: assignedUserId }
      );
      
      if (response.status === 200) {
        onUpdate(response.data.task);
        setShowAssignModal(false);
        toast.success('Subtask assigned successfully');
      }
    } catch (error) {
      toast.error(`Failed to assign subtask: ${error.response?.data?.message || error.message}`);
    }
  };

  const getStatusColor = () => {
    const status = subtask.status || 'pending';
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'in_progress': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    const status = subtask.status || 'pending';
    switch (status) {
      case 'completed': return <LuCheck className="w-4 h-4" />;
      case 'in_progress': return <LuPlay className="w-4 h-4" />;
      default: return <LuClock className="w-4 h-4" />;
    }
  };

  const canUserModify = () => {
    if (!currentUser) return false;
    return !subtask.assignedTo ||
           subtask.assignedTo._id === currentUser._id ||
           currentUser._id === taskId; // Task creator can always modify
  };

  return (
    <div className="border border-gray-200 rounded-xl p-4 hover:shadow-sm transition-all duration-200">
      {/* Main Subtask Row */}
      <div className="flex items-center gap-3">
        {/* Status Indicator */}
        <div className={`px-2 py-1 rounded-lg border text-xs font-medium flex items-center gap-1 ${getStatusColor()}`}>
          {getStatusIcon()}
          {(subtask.status || 'pending').replace('_', ' ')}
        </div>

        {/* Subtask Text */}
        <div className="flex-1">
          <span className={`font-medium ${subtask.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
            {subtask.text}
          </span>
          
          {/* Assignment Info */}
          {subtask.assignedTo && (
            <div className="flex items-center gap-1 mt-1 text-xs text-gray-600">
              <LuUser className="w-3 h-3" />
              Assigned to {subtask.assignedTo.name}
            </div>
          )}
        </div>

        {/* Time Tracking */}
        {(subtask.estimatedHours || subtask.actualHours) && (
          <div className="text-xs text-gray-600 flex items-center gap-1">
            <LuClock className="w-3 h-3" />
            {subtask.actualHours ? `${subtask.actualHours}h` : `~${subtask.estimatedHours}h`}
          </div>
        )}

        {/* Simplified Action Buttons */}
        {canUserModify() && (
          <div className="flex items-center gap-2">
            {/* Single Complete/Incomplete Toggle */}
            {!subtask.completed ? (
              <button
                onClick={() => updateSubtaskStatus('completed')}
                disabled={isUpdating}
                className="flex items-center gap-1 px-3 py-1.5 text-sm text-green-600 hover:bg-green-50 rounded-lg transition-colors border border-green-200"
                title="Mark complete"
              >
                <LuCheck className="w-4 h-4" />
                Complete
              </button>
            ) : (
              <button
                onClick={() => updateSubtaskStatus('pending')}
                disabled={isUpdating}
                className="flex items-center gap-1 px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors border border-gray-200"
                title="Mark incomplete"
              >
                Undo
              </button>
            )}

            {/* Optional Assignment - Only show if not assigned or if user can reassign */}
            {(!subtask.assignedTo || subtask.assignedTo._id === currentUser?._id) && (
              <button
                onClick={() => setShowAssignModal(true)}
                className="p-1.5 text-gray-500 hover:bg-gray-50 rounded-lg transition-colors"
                title="Assign to someone"
              >
                <LuUserPlus className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>

      {/* Simple Assignment Modal */}
      {showAssignModal && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900">Assign to:</h4>
            <button
              onClick={() => setShowAssignModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="space-y-2 max-h-32 overflow-y-auto">
            {taskAssignees.map((assignee) => (
              <button
                key={assignee._id}
                onClick={() => assignSubtask(assignee._id)}
                className="w-full flex items-center gap-2 p-2 text-left hover:bg-gray-50 rounded-lg transition-colors"
              >
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                  {assignee.name.charAt(0).toUpperCase()}
                </div>
                <span className="text-sm text-gray-900">{assignee.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedSubtaskItem;
