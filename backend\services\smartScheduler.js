import AttachmentAnalyzer from './attachmentAnalyzer.js';

/**
 * Smart Scheduler Service
 * Creates intelligent work schedules based on AI analysis and attachment complexity
 */
class SmartScheduler {
  constructor() {
    this.attachmentAnalyzer = new AttachmentAnalyzer();
    this.workingHours = {
      start: 9, // 9 AM
      end: 17,  // 5 PM
      lunchBreak: { start: 12, end: 13 } // 12-1 PM
    };
  }

  /**
   * Generate comprehensive smart schedule for a task
   */
  async generateSmartSchedule(taskData, aiAnalysis) {
    try {
      // Analyze attachments if not already done
      const attachmentAnalysis = aiAnalysis.attachmentAnalysis || 
        await this.attachmentAnalyzer.analyzeTaskAttachments(taskData.attachments || []);

      // Calculate optimal start dates
      const optimalStartDates = this.calculateOptimalStartDates(taskData, aiAnalysis, attachmentAnalysis);
      
      // Generate time-blocked schedule
      const timeBlockedSchedule = this.generateTimeBlockedSchedule(taskData, attachmentAnalysis, optimalStartDates[0]);
      
      // Create work sessions
      const workSessions = this.createWorkSessions(taskData, attachmentAnalysis, optimalStartDates[0]);
      
      // Generate productivity recommendations
      const productivityTips = this.generateProductivityRecommendations(attachmentAnalysis);

      return {
        taskId: taskData.id,
        taskTitle: taskData.title,
        optimalStartDates,
        timeBlockedSchedule,
        workSessions,
        totalEstimatedTime: attachmentAnalysis.totalProcessingTime || 30,
        complexityLevel: attachmentAnalysis.totalComplexity,
        cognitiveLoadDistribution: attachmentAnalysis.schedulingRecommendations.cognitiveLoadDistribution,
        productivityTips,
        resourceRequirements: this.extractResourceRequirements(attachmentAnalysis),
        breakRecommendations: this.generateBreakRecommendations(attachmentAnalysis),
        createdAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Smart Scheduler Error:', error);
      return this.generateFallbackSchedule(taskData);
    }
  }

  /**
   * Calculate optimal start dates based on due date and complexity
   */
  calculateOptimalStartDates(taskData, aiAnalysis, attachmentAnalysis) {
    const dueDate = new Date(taskData.dueDate);
    const today = new Date();
    const totalTime = attachmentAnalysis.totalProcessingTime || 30; // minutes
    const complexity = attachmentAnalysis.totalComplexity;
    
    // Calculate buffer days based on complexity
    let bufferDays = 1;
    if (complexity === 'High') bufferDays = 3;
    else if (complexity === 'Medium') bufferDays = 2;
    
    // Calculate working days needed
    const workingDaysNeeded = Math.ceil(totalTime / (8 * 60)); // 8 hours per day
    const totalDaysNeeded = workingDaysNeeded + bufferDays;
    
    // Generate optimal start dates
    const optimalDates = [];
    
    // Primary recommendation: Start early enough to finish comfortably
    const primaryStart = new Date(dueDate);
    primaryStart.setDate(primaryStart.getDate() - totalDaysNeeded);
    if (primaryStart > today) {
      optimalDates.push(this.formatDate(primaryStart));
    }
    
    // Secondary recommendation: Start tomorrow if primary is too early
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    if (tomorrow < dueDate && !optimalDates.includes(this.formatDate(tomorrow))) {
      optimalDates.push(this.formatDate(tomorrow));
    }
    
    // Fallback: Start today if urgent
    if (optimalDates.length === 0) {
      optimalDates.push(this.formatDate(today));
    }
    
    return optimalDates;
  }

  /**
   * Generate time-blocked schedule with specific hours
   */
  generateTimeBlockedSchedule(taskData, attachmentAnalysis, startDate) {
    const schedule = [];
    const attachments = attachmentAnalysis.attachmentAnalysis || [];
    
    if (attachments.length === 0) {
      // Simple task without attachments
      schedule.push({
        date: startDate,
        timeSlot: '9:00 AM - 11:00 AM',
        activity: 'Complete task checklist',
        duration: 120,
        cognitiveLoad: 'Medium',
        type: 'work'
      });
      return schedule;
    }
    
    // Sort attachments by optimal processing order
    const sortedAttachments = [...attachments].sort((a, b) => a.schedulingPriority - b.schedulingPriority);
    
    let currentTime = 9; // Start at 9 AM
    const currentDate = new Date(startDate);
    
    sortedAttachments.forEach((attachment, index) => {
      const duration = attachment.processingTime;
      const endTime = currentTime + (duration / 60); // Convert minutes to hours
      
      // Check if we need to move to next day
      if (endTime > this.workingHours.end || 
          (currentTime < this.workingHours.lunchBreak.end && endTime > this.workingHours.lunchBreak.start)) {
        currentDate.setDate(currentDate.getDate() + 1);
        currentTime = this.workingHours.start;
      }
      
      // Skip lunch break
      if (currentTime < this.workingHours.lunchBreak.end && currentTime + (duration / 60) > this.workingHours.lunchBreak.start) {
        currentTime = this.workingHours.lunchBreak.end;
      }
      
      const finalEndTime = currentTime + (duration / 60);
      
      schedule.push({
        date: this.formatDate(currentDate),
        timeSlot: `${this.formatTime(currentTime)} - ${this.formatTime(finalEndTime)}`,
        activity: `Process ${attachment.type}: ${attachment.filename}`,
        duration: duration,
        cognitiveLoad: attachment.cognitiveLoad,
        type: 'attachment',
        requirements: attachment.requirements,
        insights: attachment.insights
      });
      
      currentTime = finalEndTime;
      
      // Add break if needed
      if (attachment.cognitiveLoad === 'High' && index < sortedAttachments.length - 1) {
        schedule.push({
          date: this.formatDate(currentDate),
          timeSlot: `${this.formatTime(currentTime)} - ${this.formatTime(currentTime + 0.25)}`,
          activity: 'Break - Rest and recharge',
          duration: 15,
          cognitiveLoad: 'None',
          type: 'break'
        });
        currentTime += 0.25;
      }
    });
    
    // Add final task completion session
    if (currentTime + 1 > this.workingHours.end) {
      currentDate.setDate(currentDate.getDate() + 1);
      currentTime = this.workingHours.start;
    }
    
    schedule.push({
      date: this.formatDate(currentDate),
      timeSlot: `${this.formatTime(currentTime)} - ${this.formatTime(currentTime + 1)}`,
      activity: 'Complete task checklist and finalize',
      duration: 60,
      cognitiveLoad: 'Medium',
      type: 'completion'
    });
    
    return schedule;
  }

  /**
   * Create focused work sessions
   */
  createWorkSessions(taskData, attachmentAnalysis, startDate) {
    const sessions = [];
    const totalTime = attachmentAnalysis.totalProcessingTime || 30;
    const complexity = attachmentAnalysis.totalComplexity;
    
    // Determine session length based on complexity
    let sessionLength = 45; // minutes
    if (complexity === 'High') sessionLength = 25; // Pomodoro technique
    else if (complexity === 'Low') sessionLength = 90; // Longer focus blocks
    
    const numberOfSessions = Math.ceil(totalTime / sessionLength);
    const currentDate = new Date(startDate);
    
    for (let i = 0; i < numberOfSessions; i++) {
      const sessionStart = 9 + (i * 2); // 2-hour gaps between sessions
      
      // Move to next day if needed
      if (sessionStart >= this.workingHours.end) {
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      const actualStart = sessionStart >= this.workingHours.end ? 9 : sessionStart;
      
      sessions.push({
        sessionNumber: i + 1,
        date: this.formatDate(currentDate),
        startTime: this.formatTime(actualStart),
        endTime: this.formatTime(actualStart + (sessionLength / 60)),
        duration: sessionLength,
        focus: this.getSessionFocus(i, attachmentAnalysis),
        energyLevel: this.getOptimalEnergyLevel(actualStart),
        breakAfter: sessionLength >= 45 ? 15 : 5
      });
    }
    
    return sessions;
  }

  /**
   * Generate productivity recommendations
   */
  generateProductivityRecommendations(attachmentAnalysis) {
    const tips = [];
    const attachments = attachmentAnalysis.attachmentAnalysis || [];
    
    // General tips
    tips.push('🎯 Start with the most complex files when your energy is highest');
    tips.push('⏰ Use the Pomodoro Technique for high cognitive load tasks');
    tips.push('🔄 Take regular breaks to maintain focus and prevent fatigue');
    
    // Attachment-specific tips
    if (attachments.some(a => a.type === 'PDF')) {
      tips.push('📚 Use active reading techniques: highlight, take notes, summarize');
    }
    
    if (attachments.some(a => a.type === 'Video')) {
      tips.push('🎥 Watch videos at 1.25x speed for better engagement');
      tips.push('📝 Pause frequently to take notes and process information');
    }
    
    if (attachments.some(a => a.type === 'Image')) {
      tips.push('🖼️ Analyze images systematically: overview, details, interpretation');
    }
    
    // Cognitive load tips
    if (attachmentAnalysis.totalComplexity === 'High') {
      tips.push('🧠 Schedule complex work during your peak mental hours');
      tips.push('🚫 Minimize distractions and notifications during focus time');
    }
    
    return tips;
  }

  /**
   * Helper methods
   */
  formatDate(date) {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  formatTime(hour) {
    const wholeHour = Math.floor(hour);
    const minutes = Math.round((hour - wholeHour) * 60);
    const period = wholeHour >= 12 ? 'PM' : 'AM';
    const displayHour = wholeHour > 12 ? wholeHour - 12 : wholeHour === 0 ? 12 : wholeHour;
    return `${displayHour}:${String(minutes).padStart(2, '0')} ${period}`;
  }

  extractResourceRequirements(attachmentAnalysis) {
    const requirements = new Set();
    
    attachmentAnalysis.attachmentAnalysis.forEach(analysis => {
      analysis.requirements.forEach(req => requirements.add(req));
    });
    
    return Array.from(requirements);
  }

  generateBreakRecommendations(attachmentAnalysis) {
    const recommendations = [];
    const totalTime = attachmentAnalysis.totalProcessingTime;
    
    if (totalTime > 120) { // More than 2 hours
      recommendations.push('Take a 15-minute break every hour');
      recommendations.push('Include a longer 30-minute break halfway through');
    } else if (totalTime > 60) { // More than 1 hour
      recommendations.push('Take a 10-minute break every 45 minutes');
    } else {
      recommendations.push('Take a 5-minute break if needed');
    }
    
    return recommendations;
  }

  getSessionFocus(sessionIndex, attachmentAnalysis) {
    const attachments = attachmentAnalysis.attachmentAnalysis || [];
    if (attachments.length > sessionIndex) {
      return `Focus on ${attachments[sessionIndex].filename}`;
    }
    return 'Complete remaining checklist items';
  }

  getOptimalEnergyLevel(hour) {
    if (hour >= 9 && hour <= 11) return 'High';
    if (hour >= 14 && hour <= 16) return 'Medium-High';
    if (hour >= 11 && hour <= 12) return 'Medium';
    return 'Low';
  }

  generateFallbackSchedule(taskData) {
    return {
      taskId: taskData.id,
      taskTitle: taskData.title,
      optimalStartDates: [this.formatDate(new Date())],
      timeBlockedSchedule: [{
        date: this.formatDate(new Date()),
        timeSlot: '9:00 AM - 11:00 AM',
        activity: 'Complete task',
        duration: 120,
        cognitiveLoad: 'Medium',
        type: 'work'
      }],
      workSessions: [{
        sessionNumber: 1,
        date: this.formatDate(new Date()),
        startTime: '9:00 AM',
        endTime: '11:00 AM',
        duration: 120,
        focus: 'Complete task checklist',
        energyLevel: 'High',
        breakAfter: 15
      }],
      totalEstimatedTime: 120,
      complexityLevel: 'Medium',
      cognitiveLoadDistribution: 'Even',
      productivityTips: ['Focus on one task at a time', 'Take breaks as needed'],
      resourceRequirements: ['Basic tools'],
      breakRecommendations: ['Take breaks as needed'],
      createdAt: new Date().toISOString()
    };
  }
}

export default SmartScheduler;
