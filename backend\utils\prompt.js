export const generatePrioritizationPrompt = (
  title,
  description,
  priority,
  dueDate,
  assignedTo,
  attachments,
  todoChecklist
) => `
You are an AI assistant trained to provide intelligent task prioritization analysis. Your goal is to:

1. **Analyze the user's chosen priority** (${priority}) and determine if it aligns with objective factors
2. **Recommend an AI-suggested priority** based on urgency, complexity, and workload
3. **Provide priority reconciliation** - explain any differences and suggest the optimal approach
4. **Suggest specific start dates** with reasoning
5. **Calculate estimated work duration** based on checklist complexity

---

**Task Details:**
- **Title:** ${title}
- **Description:** ${description}
- **User's Priority:** ${priority}
- **Due Date:** ${dueDate}
- **Assigned To:** ${assignedTo?.join(", ") || "None"}
- **Attachments Count:** ${attachments?.length || 0}
- **Checklist Items (${todoChecklist?.length || 0} total):**
${todoChecklist?.map((item, index) => `  ${index + 1}. ${item.text} [${item.completed ? "Completed" : "Pending"}]`).join("\n") || "  None"}

---

**Analysis Framework:**
- **Urgency:** Days until due date
- **Complexity:** Number and nature of checklist items
- **Workload:** Estimated hours needed
- **Collaboration:** Number of people involved

**Respond in this exact format:**

AI Priority: <High/Medium/Low>
User Priority: ${priority}
Priority Match: <ALIGNED/CONFLICT>
Recommended Priority: <High/Medium/Low>
Priority Reasoning: <Brief explanation of why this priority is optimal>

Estimated Duration: <X hours/days>
Suggested Start Date(s): <one or more specific dates before ${dueDate}, in DD/MM/YYYY format>
Start Date Reasoning: <Why these dates are optimal>

Workload Breakdown: <Brief analysis of task complexity>
Risk Assessment: <Potential challenges or delays>
`;




export const generateEnhancedPromptWithAttachments = (taskData, attachmentAnalysis) => `
You are an advanced AI task management assistant with multimedia analysis capabilities.

**Task Information:**
- Title: ${taskData.title}
- Description: ${taskData.description}
- User Priority: ${taskData.priority}
- Due Date: ${taskData.dueDate}
- Checklist Items: ${taskData.todoChecklist?.length || 0}

**Attachment Analysis:**
${attachmentAnalysis.attachmentAnalysis.length > 0 ?
  attachmentAnalysis.attachmentAnalysis.map(analysis => `
📎 **${analysis.type} File: ${analysis.filename}**
   - Complexity Level: ${analysis.complexity}
   - Processing Time: ${analysis.processingTime} minutes
   - Cognitive Load: ${analysis.cognitiveLoad}
   - Optimal Time Slot: ${analysis.optimalTimeSlot}
   - Insights: ${analysis.insights}
   - Requirements: ${analysis.requirements.join(', ')}
`).join('\n') :
  '📎 **No Attachments** - Standard task processing'
}

**Attachment Summary:**
- Total Complexity: ${attachmentAnalysis.totalComplexity}
- Total Processing Time: ${attachmentAnalysis.totalProcessingTime} minutes
- Cognitive Load Distribution: ${attachmentAnalysis.schedulingRecommendations.cognitiveLoadDistribution}
- Suggested Breaks: ${attachmentAnalysis.schedulingRecommendations.suggestedBreaks}

**Advanced Analysis Required:**
1. **Priority Assessment**: Consider attachment complexity in priority calculation
2. **Time Estimation**: Factor in file processing, reading, and analysis time
3. **Work Schedule**: Suggest optimal timing based on content type and cognitive load
4. **Resource Requirements**: Identify tools/software needed for attachments
5. **Cognitive Load**: Assess mental effort required for different file types
6. **Sequential Dependencies**: Determine if attachments must be processed in order

**Response Format:**
AI Priority: <High/Medium/Low>
User Priority: ${taskData.priority}
Priority Match: <ALIGNED/CONFLICT>
Recommended Priority: <High/Medium/Low>
Priority Reasoning: <Include attachment complexity factors>

Estimated Duration: <Total time including attachment processing>
Attachment Processing Time: <Breakdown by file type>
Suggested Start Date(s): <one or more specific dates before ${taskData.dueDate}, in DD/MM/YYYY format>
Start Date Reasoning: <Why these dates are optimal considering attachments>
Suggested Work Schedule: <Optimal timing considering file types and cognitive load>

Workload Breakdown: <Include attachment-specific tasks>
Resource Requirements: <Software/tools needed for attachments>
Cognitive Load Assessment: <Mental effort required and distribution strategy>
Risk Assessment: <File-related challenges and solutions>
`;

export const generatePrioritizationExplanationPrompt = (
  title,
  description,
  priority,
  dueDate,
  assignedTo,
  attachments,
  todoChecklist
) => `
You are an AI assistant designed to justify task prioritization decisions. Given the task details below, explain clearly **why a specific priority level (High / Medium / Low)** is appropriate. Consider due date urgency, task complexity, number of checklist items, and team involvement.

---

**Task Details:**

- **Title:** ${title}
- **Description:** ${description}
- **Current Priority:** ${priority}
- **Due Date:** ${dueDate}
- **Assigned To:** ${assignedTo?.join(", ") || "None"}
- **Attachments Count:** ${attachments?.length || 0}
- **Checklist Items:**
${todoChecklist?.map((item, index) => `  ${index + 1}. ${item.text} [${item.completed ? "Completed" : "Pending"}]`).join("\n") || "  None"}

---

Explain the reasoning for setting this task's priority level. The explanation should be 3–5 sentences long and based on actual task factors.
`;


