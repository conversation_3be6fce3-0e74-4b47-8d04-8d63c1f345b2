import axios from 'axios';

const BASE_URL = 'http://localhost:8000/api';

const testEndpoints = async () => {
  try {
    console.log('🧪 Testing Friend Request Endpoints...\n');
    
    // Test 1: Check if friend-requests endpoints are accessible (should return 401 without auth)
    console.log('📡 Test 1: Checking endpoint accessibility...');
    
    try {
      await axios.get(`${BASE_URL}/friend-requests/received`);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ /friend-requests/received endpoint is accessible (returns 401 as expected)');
      } else {
        console.log('❌ Unexpected response:', error.response?.status);
      }
    }
    
    try {
      await axios.get(`${BASE_URL}/friend-requests/sent`);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ /friend-requests/sent endpoint is accessible (returns 401 as expected)');
      } else {
        console.log('❌ Unexpected response:', error.response?.status);
      }
    }
    
    // Test 2: Check server health
    console.log('\n🏥 Test 2: Checking server health...');
    
    try {
      const healthCheck = await axios.get(`${BASE_URL}/users`);
      console.log('❌ /users endpoint should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Server is running and authentication is working');
      } else {
        console.log('❌ Unexpected server response:', error.response?.status);
      }
    }
    
    console.log('\n🎉 Endpoint Test Completed!');
    console.log('✅ Friend request endpoints are properly configured');
    console.log('💡 To test full functionality, use the frontend or provide valid authentication tokens');
    
  } catch (error) {
    console.error('❌ Error testing endpoints:', error.message);
  }
};

testEndpoints();
