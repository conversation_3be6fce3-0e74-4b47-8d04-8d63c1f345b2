import React, { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './AISmartSchedule.css';

const localizer = momentLocalizer(moment);

const AISmartSchedule = () => {
  const [tasks, setTasks] = useState([]);
  const [smartSchedules, setSmartSchedules] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [view, setView] = useState('month');
  const [selectedDate, setSelectedDate] = useState(new Date());

  useEffect(() => {
    fetchTasksAndSchedules();
  }, []);

  const fetchTasksAndSchedules = async () => {
    try {
      setLoading(true);
      
      // Fetch user tasks
      const tasksResponse = await fetch('http://localhost:8000/api/tasks', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (tasksResponse.ok) {
        const tasksData = await tasksResponse.json();
        setTasks(tasksData);
        
        // Generate smart schedules for each task
        await generateSmartSchedules(tasksData);
      }
    } catch (error) {
      console.error('Error fetching tasks and schedules:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateSmartSchedules = async (tasksData) => {
    const schedules = [];
    
    for (const task of tasksData) {
      try {
        // Get AI analysis for the task
        const aiResponse = await fetch('http://localhost:8000/api/ai/prioritize', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            title: task.title,
            description: task.description,
            priority: task.priority,
            dueDate: task.dueDate,
            attachments: task.attachments || [],
            todoChecklist: task.todoChecklist || []
          })
        });

        if (aiResponse.ok) {
          const aiData = await aiResponse.json();
          
          // Create smart schedule events
          const scheduleEvents = createScheduleEvents(task, aiData);
          schedules.push(...scheduleEvents);
        }
      } catch (error) {
        console.error(`Error generating schedule for task ${task.title}:`, error);
      }
    }
    
    setSmartSchedules(schedules);
  };

  const createScheduleEvents = (task, aiData) => {
    const events = [];
    const suggestedDates = aiData.suggestedDates || [];
    const smartScheduling = aiData.smartScheduling || {};
    
    // Create main work session events
    suggestedDates.forEach((dateStr, index) => {
      const [day, month, year] = dateStr.split('/').map(Number);
      const startDate = new Date(year, month - 1, day, 9, 0); // 9:00 AM
      const endDate = new Date(year, month - 1, day, 11, 0);   // 11:00 AM
      
      events.push({
        id: `${task._id}-work-${index}`,
        title: `🎯 ${task.title}`,
        start: startDate,
        end: endDate,
        resource: {
          type: 'work',
          taskId: task._id,
          task: task,
          aiData: aiData,
          complexity: smartScheduling.totalComplexity || 'Medium',
          estimatedTime: smartScheduling.totalProcessingTime || 30,
          attachments: task.attachments || []
        }
      });
    });

    // Create attachment processing events
    if (aiData.attachmentAnalysis && aiData.attachmentAnalysis.length > 0) {
      aiData.attachmentAnalysis.forEach((attachment, index) => {
        const [day, month, year] = suggestedDates[0]?.split('/').map(Number) || [new Date().getDate(), new Date().getMonth() + 1, new Date().getFullYear()];
        const startDate = new Date(year, month - 1, day, 14 + index, 0); // Afternoon slots
        const endDate = new Date(year, month - 1, day, 14 + index, attachment.processingTime || 30);
        
        events.push({
          id: `${task._id}-attachment-${index}`,
          title: `📎 ${attachment.filename}`,
          start: startDate,
          end: endDate,
          resource: {
            type: 'attachment',
            taskId: task._id,
            task: task,
            attachment: attachment,
            complexity: attachment.complexity,
            cognitiveLoad: attachment.cognitiveLoad
          }
        });
      });
    }

    return events;
  };

  const eventStyleGetter = (event) => {
    let backgroundColor = '#3174ad';
    let borderColor = '#3174ad';
    
    switch (event.resource.type) {
      case 'work':
        backgroundColor = '#4CAF50'; // Green for main work
        borderColor = '#45a049';
        break;
      case 'attachment':
        backgroundColor = '#FF9800'; // Orange for attachments
        borderColor = '#f57c00';
        break;
      case 'break':
        backgroundColor = '#9C27B0'; // Purple for breaks
        borderColor = '#7b1fa2';
        break;
      default:
        backgroundColor = '#2196F3'; // Blue default
        borderColor = '#1976d2';
    }
    
    // Adjust opacity based on complexity
    const complexity = event.resource.complexity;
    let opacity = 0.8;
    if (complexity === 'High') opacity = 1.0;
    else if (complexity === 'Low') opacity = 0.6;
    
    return {
      style: {
        backgroundColor,
        borderColor,
        opacity,
        color: 'white',
        border: '2px solid ' + borderColor,
        borderRadius: '8px',
        fontSize: '12px',
        fontWeight: '500'
      }
    };
  };

  const handleSelectEvent = (event) => {
    setSelectedTask({
      ...event.resource,
      event: event
    });
  };

  const handleSelectSlot = ({ start, end }) => {
    // Future feature: Allow creating new tasks by clicking on calendar slots
    console.log('Selected slot:', start, end);
  };

  const CustomToolbar = ({ label, onNavigate, onView }) => (
    <div className="ai-calendar-toolbar">
      <div className="toolbar-navigation">
        <button onClick={() => onNavigate('PREV')} className="nav-button">
          ← Previous
        </button>
        <h2 className="calendar-title">{label}</h2>
        <button onClick={() => onNavigate('NEXT')} className="nav-button">
          Next →
        </button>
      </div>
      
      <div className="toolbar-views">
        <button 
          onClick={() => onView('month')} 
          className={`view-button ${view === 'month' ? 'active' : ''}`}
        >
          Month
        </button>
        <button 
          onClick={() => onView('week')} 
          className={`view-button ${view === 'week' ? 'active' : ''}`}
        >
          Week
        </button>
        <button 
          onClick={() => onView('day')} 
          className={`view-button ${view === 'day' ? 'active' : ''}`}
        >
          Day
        </button>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="ai-schedule-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Generating AI Smart Schedule...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-schedule-container">
      <div className="ai-schedule-header">
        <h1>🧠 AI Smart Schedule</h1>
        <p>Intelligent work scheduling based on attachment analysis and cognitive load optimization</p>
        
        <div className="schedule-legend">
          <div className="legend-item">
            <span className="legend-color work"></span>
            <span>Main Work Sessions</span>
          </div>
          <div className="legend-item">
            <span className="legend-color attachment"></span>
            <span>Attachment Processing</span>
          </div>
          <div className="legend-item">
            <span className="legend-color break"></span>
            <span>Recommended Breaks</span>
          </div>
        </div>
      </div>

      <div className="calendar-wrapper">
        <Calendar
          localizer={localizer}
          events={smartSchedules}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 600 }}
          onSelectEvent={handleSelectEvent}
          onSelectSlot={handleSelectSlot}
          selectable
          eventPropGetter={eventStyleGetter}
          view={view}
          onView={setView}
          date={selectedDate}
          onNavigate={setSelectedDate}
          components={{
            toolbar: CustomToolbar
          }}
          formats={{
            timeGutterFormat: 'HH:mm',
            eventTimeRangeFormat: ({ start, end }) => 
              `${moment(start).format('HH:mm')} - ${moment(end).format('HH:mm')}`
          }}
        />
      </div>

      {selectedTask && (
        <div className="task-details-modal">
          <div className="modal-content">
            <div className="modal-header">
              <h3>{selectedTask.event.title}</h3>
              <button 
                className="close-button"
                onClick={() => setSelectedTask(null)}
              >
                ×
              </button>
            </div>
            
            <div className="modal-body">
              {selectedTask.type === 'work' && (
                <div className="work-details">
                  <p><strong>Task:</strong> {selectedTask.task.title}</p>
                  <p><strong>Description:</strong> {selectedTask.task.description}</p>
                  <p><strong>Complexity:</strong> {selectedTask.complexity}</p>
                  <p><strong>Estimated Time:</strong> {selectedTask.estimatedTime} minutes</p>
                  <p><strong>Attachments:</strong> {selectedTask.attachments.length}</p>
                  
                  {selectedTask.aiData && (
                    <div className="ai-insights">
                      <h4>AI Insights:</h4>
                      <p><strong>Priority:</strong> {selectedTask.aiData.recommendedPriority}</p>
                      <p><strong>Reasoning:</strong> {selectedTask.aiData.priorityReasoning}</p>
                    </div>
                  )}
                </div>
              )}
              
              {selectedTask.type === 'attachment' && (
                <div className="attachment-details">
                  <p><strong>File:</strong> {selectedTask.attachment.filename}</p>
                  <p><strong>Type:</strong> {selectedTask.attachment.type}</p>
                  <p><strong>Complexity:</strong> {selectedTask.attachment.complexity}</p>
                  <p><strong>Processing Time:</strong> {selectedTask.attachment.processingTime} minutes</p>
                  <p><strong>Cognitive Load:</strong> {selectedTask.attachment.cognitiveLoad}</p>
                  <p><strong>Insights:</strong> {selectedTask.attachment.insights}</p>
                  
                  <div className="requirements">
                    <h4>Requirements:</h4>
                    <ul>
                      {selectedTask.attachment.requirements.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AISmartSchedule;
