import React, { useEffect, useState, useContext } from "react";
import DashboardLayout from "../../components/layouts/DashboardLayout";
import { API_PATHS } from "../../utils/apiPaths";
import axiosInstance from "../../utils/axiosInstance";
import { UserContext } from "../../context/userContext";
import { useNotifications } from "../../contexts/NotificationContext";
import FriendRequestManager from "../../components/Friends/FriendRequestManager";
import { toast } from "react-toastify";
import {
  LuFileSpreadsheet,
  LuSearch,
  LuUsers,
  LuUserPlus,
  LuFilter,
  LuGrid3X3,
  LuList,
  LuStar,
  LuMessageCircle,
  LuUserCheck,
  LuUserX,
  LuTrendingUp,
  LuCalendar,
  LuMail,
  LuClock,
  LuCheck,
  LuCircle,
  LuX
} from "react-icons/lu";
import UserCard from "../../components/Cards/UserCard";
import { Link } from "react-router-dom";

const ManageUsers = () => {
  const { user: contextUser } = useContext(UserContext);
  const { refreshNotifications } = useNotifications();
  const [currentUser, setCurrentUser] = useState(null);
  const [allUsers, setAllUsers] = useState([]);
  const [friends, setFriends] = useState([]);
  const [view, setView] = useState("friends");
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [filterBy, setFilterBy] = useState("all");
  const [viewMode, setViewMode] = useState("grid");
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFriend, setSelectedFriend] = useState(null);
  const [friendTasks, setFriendTasks] = useState([]);
  const [loadingTasks, setLoadingTasks] = useState(false);
  const [userTasks, setUserTasks] = useState([]);
  const [loadingUserTasks, setLoadingUserTasks] = useState(false);
  const [draggedTask, setDraggedTask] = useState(null);
  const [isTaskPanelOpen, setIsTaskPanelOpen] = useState(false);
  const [assigningTask, setAssigningTask] = useState(false);
  const [sentFriendRequests, setSentFriendRequests] = useState(new Set());
  const [friendRequestRefreshTrigger, setFriendRequestRefreshTrigger] = useState(0);

  useEffect(() => {
    const fetchAllData = async () => {
      try {
        // Fetch current user (fallback if context fails)
        let user = contextUser;
        console.log("Context user:", contextUser);

        if (!user) {
          console.log("Fetching user from API...");
          try {
            const profileRes = await axiosInstance.get("/api/auth/profile");
            user = profileRes.data;
            console.log("User fetched from API:", user);
            setCurrentUser(user);
          } catch (error) {
            console.error("Failed to fetch user from API:", error);
            // TEMPORARY HARDCODED FIX - Use the known user ID from the task
            user = { _id: "686899e58df03c0034242553", name: "Luqman" };
            console.log("Using hardcoded user as fallback:", user);
            setCurrentUser(user);
          }
        } else {
          console.log("Using context user:", user);
          setCurrentUser(user);
        }

        // Fetch all users
        const allRes = await axiosInstance.get(API_PATHS.USERS.GET_ALL_USERS);
        setAllUsers(allRes.data);

        // Fetch user's friends (should be full user objects)
        const friendsRes = await axiosInstance.get("/api/users/friends");
        setFriends(friendsRes.data);

        // Fetch sent friend requests to track pending requests
        try {
          const sentRequestsRes = await axiosInstance.get("/api/friend-requests/sent");
          const pendingRequestIds = new Set(
            sentRequestsRes.data
              .filter(request => request.status === 'PENDING')
              .map(request => request.recipient._id || request.recipient)
          );
          setSentFriendRequests(pendingRequestIds);
        } catch (error) {
          console.error("Error fetching sent friend requests:", error);
        }

        // Note: fetchUserTasks will be called in separate useEffect when currentUser is set
      } catch (err) {
        console.error("Error fetching user data:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  // Fetch user tasks when currentUser is available
  useEffect(() => {
    if (currentUser) {
      console.log("Current user is now available, fetching tasks...");
      fetchUserTasks();
    }
  }, [currentUser]);

  // Periodically refresh friend request status to handle accepted/declined requests
  useEffect(() => {
    const interval = setInterval(() => {
      refreshFriendRequestStatus();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Debug task data to check profile pictures
  useEffect(() => {
    if (userTasks.length > 0) {
      console.log("=== TASK PROFILE PICTURE DEBUG ===");
      userTasks.forEach((task, index) => {
        console.log(`Task ${index + 1}:`, {
          title: task.title,
          createdBy: task.createdBy,
          createdByType: typeof task.createdBy,
          profileImageUrl: task.createdBy?.profileImageUrl,
          name: task.createdBy?.name
        });
      });
    }
  }, [userTasks]);

  // Debug: Log user data to see task counts
  useEffect(() => {
    if (friends.length > 0) {
      console.log("=== FRIEND TASK COUNTS DEBUG ===");
      friends.forEach(friend => {
        console.log(`${friend.name}:`, {
          pendingTasks: friend.pendingTasks || 0,
          inProgressTasks: friend.inProgressTasks || 0,
          completedTasks: friend.completedTasks || 0,
          total: (friend.pendingTasks || 0) + (friend.inProgressTasks || 0) + (friend.completedTasks || 0)
        });
      });
      console.log("=== END FRIEND TASK COUNTS DEBUG ===");
    }
  }, [friends]);

  // Fetch user's unassigned tasks for drag and drop
  const fetchUserTasks = async () => {
    setLoadingUserTasks(true);
    try {
      const response = await axiosInstance.get("/api/tasks");
      console.log("User tasks API Response:", response.data);

      // Handle different response structures
      let allTasks = [];
      if (Array.isArray(response.data)) {
        allTasks = response.data;
      } else if (response.data && Array.isArray(response.data.tasks)) {
        allTasks = response.data.tasks;
      } else if (response.data && Array.isArray(response.data.data)) {
        allTasks = response.data.data;
      } else {
        allTasks = [];
      }

      // Show ALL tasks that the current user can assign to friends
      // This ensures users can drag the same task to multiple friends
      const assignableTasks = allTasks.filter(task => {
        // Get current user ID - try multiple sources
        const userId = currentUser?._id || localStorage.getItem('userId') || sessionStorage.getItem('userId');

        // Extract createdBy ID (handle both string and object formats)
        const taskCreatorId = typeof task.createdBy === 'string' ? task.createdBy : task.createdBy?._id;

        // Show tasks created by the current user (they can always assign their own tasks)
        if (taskCreatorId === userId) {
          return true;
        }

        // Show tasks assigned to the current user (they can share with friends)
        if (task.assignedTo && task.assignedTo.length > 0) {
          return task.assignedTo.some(assignee => {
            const assigneeId = typeof assignee === 'string' ? assignee : assignee._id;
            return assigneeId === userId;
          });
        }

        return false; // Don't show tasks from other users that aren't assigned to current user
      });

      console.log("=== TASK FILTERING DEBUG ===");
      console.log("All tasks from API:", allTasks);

      // Check multiple sources for user ID
      const userId = currentUser?._id || contextUser?._id || localStorage.getItem('userId') || sessionStorage.getItem('userId');
      console.log("Current user from context:", contextUser?._id);
      console.log("Current user from state:", currentUser?._id);
      console.log("User ID from localStorage:", localStorage.getItem('userId'));
      console.log("User ID from sessionStorage:", sessionStorage.getItem('userId'));
      console.log("Final user ID used:", userId);
      console.log("Number of tasks:", allTasks.length);

      if (allTasks.length > 0) {
        allTasks.forEach((task, index) => {
          const taskCreatorId = typeof task.createdBy === 'string' ? task.createdBy : task.createdBy?._id;
          console.log(`Task ${index + 1}: "${task.title}"`);
          console.log(`  - Task ID: ${task._id}`);
          console.log(`  - Created by (raw):`, task.createdBy);
          console.log(`  - Created by (extracted): ${taskCreatorId}`);
          console.log(`  - Assigned to:`, task.assignedTo);
          console.log(`  - Created by current user: ${taskCreatorId === userId}`);
          console.log(`  - Assigned to current user:`, task.assignedTo?.some(assignee => {
            const assigneeId = typeof assignee === 'string' ? assignee : assignee._id;
            return assigneeId === userId;
          }));
        });
      }

      console.log("Final assignable tasks:", assignableTasks);
      console.log("Number of assignable tasks:", assignableTasks.length);
      console.log("=== END TASK FILTERING DEBUG ===");
      setUserTasks(assignableTasks);
    } catch (error) {
      console.error("Error fetching user tasks:", error);
      setUserTasks([]);
    } finally {
      setLoadingUserTasks(false);
    }
  };



  // Function to refresh collaboration request status
  const refreshFriendRequestStatus = async () => {
    try {
      const sentRequestsRes = await axiosInstance.get("/api/friend-requests/sent");
      const pendingRequestIds = new Set(
        sentRequestsRes.data
          .filter(request => request.status === 'PENDING')
          .map(request => request.recipient._id || request.recipient)
      );
      setSentFriendRequests(pendingRequestIds);
    } catch (error) {
      console.error("Error refreshing collaboration request status:", error);
    }
  };

  // Helper functions for new features
  const handleAddFriend = async (userId, message = "") => {
    try {
      console.log('🔍 Sending collaboration request to:', userId, 'with message:', message);
      const response = await axiosInstance.post(`/api/friend-requests/send/${userId}`, { message });
      console.log('✅ Collaboration request response:', response.data);
      toast.success("Collaboration request sent! 📤");

      // Add the user to sent friend requests to show "Requested" button
      setSentFriendRequests(prev => new Set([...prev, userId]));

      // Refresh friends list and friend request status
      const friendsRes = await axiosInstance.get("/api/users/friends");
      setFriends(friendsRes.data);

      // Also refresh collaboration request status to handle any changes
      await refreshFriendRequestStatus();

      // Trigger refresh of FriendRequestManager to show the new sent request
      setFriendRequestRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('❌ Error sending friend request:', error);
      console.error('Error details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      if (error.response?.status === 400) {
        toast.error(error.response.data.message || "Cannot send friend request");
      } else if (error.response?.status === 404) {
        toast.error("User not found");
      } else {
        toast.error("Failed to send friend request");
      }
    }
  };

  const handleRemoveFriend = async (userId) => {
    try {
      await axiosInstance.post(`/api/users/remove-friend/${userId}`);
      toast.success("Friend removed successfully!");
      // Refresh friends list
      const friendsRes = await axiosInstance.get("/api/users/friends");
      setFriends(friendsRes.data);
    } catch (error) {
      toast.error("Failed to remove friend");
    }
  };

  // Fetch tasks assigned by current user to the selected friend
  const fetchFriendTasks = async (friendId) => {
    setLoadingTasks(true);
    try {
      // Get all tasks created by current user
      const response = await axiosInstance.get("/api/tasks");
      console.log("API Response:", response.data); // Debug log

      // Handle different response structures
      let allTasks = [];
      if (Array.isArray(response.data)) {
        allTasks = response.data;
      } else if (response.data && Array.isArray(response.data.tasks)) {
        allTasks = response.data.tasks;
      } else if (response.data && Array.isArray(response.data.data)) {
        allTasks = response.data.data;
      } else {
        console.log("Unexpected response structure:", response.data);
        allTasks = [];
      }

      console.log("All tasks:", allTasks); // Debug log

      // Filter tasks that are assigned to the selected friend
      const tasksAssignedToFriend = allTasks.filter(task => {
        if (!task.assignedTo || !Array.isArray(task.assignedTo)) {
          return false;
        }

        return task.assignedTo.some(assignee => {
          const assigneeId = typeof assignee === 'string' ? assignee : assignee._id;
          return assigneeId === friendId;
        });
      });

      console.log("Filtered tasks for friend:", tasksAssignedToFriend); // Debug log
      setFriendTasks(tasksAssignedToFriend);
    } catch (error) {
      console.error("Error fetching assigned tasks:", error);
      setFriendTasks([]);
    } finally {
      setLoadingTasks(false);
    }
  };

  // Handle friend selection
  const handleFriendSelect = (friend) => {
    setSelectedFriend(friend);
    fetchFriendTasks(friend._id);
  };

  // Handle task assignment via drag and drop
  const handleTaskAssignment = async (taskId, friendId) => {
    try {
      setAssigningTask(true);

      // Find the current task to get existing assignees
      const currentTask = userTasks.find(task => task._id === taskId);
      const existingAssignees = currentTask?.assignedTo || [];

      // Find the friend's name for better messaging
      const friend = friends.find(f => f._id === friendId);
      const friendName = friend?.name || 'friend';

      // Check if friend is already assigned
      const isAlreadyAssigned = existingAssignees.some(assignee => {
        const assigneeId = typeof assignee === 'string' ? assignee : assignee._id;
        return assigneeId === friendId;
      });

      if (isAlreadyAssigned) {
        toast.info(`Task is already assigned to ${friendName}`);
        return;
      }

      // Add friend to existing assignees
      const updatedAssignees = [...existingAssignees, friendId];

      // Update task assignment
      const response = await axiosInstance.put(`/api/tasks/${taskId}`, {
        assignedTo: updatedAssignees
      });

      if (response.status === 200) {
        toast.success(`Task "${currentTask.title}" shared with ${friendName} successfully! 📧 Notification sent.`);

        // Refresh user tasks and friend tasks
        await fetchUserTasks();
        if (selectedFriend && selectedFriend._id === friendId) {
          await fetchFriendTasks(friendId);
        }

        // Refresh notifications to show new task assignment notifications
        refreshNotifications();
      }
    } catch (error) {
      console.error("Error assigning task:", error);
      toast.error("Failed to assign task. Please try again.");
    } finally {
      setAssigningTask(false);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e, task) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e, friendId) => {
    e.preventDefault();
    if (draggedTask) {
      handleTaskAssignment(draggedTask._id, friendId);
      setDraggedTask(null);
    }
  };

  const handleBulkAddFriends = async () => {
    try {
      const promises = selectedUsers.map(userId =>
        axiosInstance.post(`/api/users/add-friend/${userId}`)
      );
      await Promise.all(promises);
      toast.success(`Added ${selectedUsers.length} friends!`);
      setSelectedUsers([]);
      // Refresh friends list
      const friendsRes = await axiosInstance.get("/api/users/friends");
      setFriends(friendsRes.data);
    } catch (error) {
      toast.error("Failed to add some friends");
    }
  };

  // Filter and sort users
  const getFilteredAndSortedUsers = () => {
    let users;

    if (view === "friends") {
      users = friends;
    } else {
      // For "Discover" view, exclude current user AND existing friends
      const friendIds = friends.map(friend => friend._id);
      users = allUsers.filter(user =>
        user._id !== currentUser?._id &&
        !friendIds.includes(user._id)
      );
    }

    // Apply search filter
    if (searchTerm) {
      users = users.filter(user =>
        user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply activity filter
    if (filterBy !== "all") {
      switch (filterBy) {
        case "active":
          users = users.filter(user => (user.completedTasks || 0) > 0);
          break;
        case "new":
          users = users.filter(user => {
            const joinDate = new Date(user.createdAt);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return joinDate > weekAgo;
          });
          break;
        case "top":
          users = users.filter(user => (user.points || 0) > 50);
          break;
        default:
          break;
      }
    }

    // Apply sorting
    users.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return (a.name || "").localeCompare(b.name || "");
        case "points":
          return (b.points || 0) - (a.points || 0);
        case "tasks":
          return (b.completedTasks || 0) - (a.completedTasks || 0);
        case "recent":
          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
        default:
          return 0;
      }
    });

    return users;
  };

  const displayedUsers = getFilteredAndSortedUsers();

  return (
    <DashboardLayout activeMenu="Team Members">
      {/* Apple-style Header */}
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">Team Members</h1>
                <p className="text-gray-600">Connect, collaborate, and grow together</p>
              </div>

              <div className="flex items-center gap-3">
                {selectedUsers.length > 0 && (
                  <button
                    onClick={handleBulkAddFriends}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    <LuUserPlus size={18} />
                    Add {selectedUsers.length} Friends
                  </button>
                )}

                {/* Task Panel Toggle Button */}
                <button
                  onClick={() => {
                    console.log('Toggle clicked, current state:', isTaskPanelOpen);
                    setIsTaskPanelOpen(!isTaskPanelOpen);
                  }}
                  className={`flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all duration-200 shadow-md ${
                    isTaskPanelOpen
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-green-500 text-white hover:bg-green-600 animate-pulse'
                  }`}
                >
                  <LuFileSpreadsheet className="w-5 h-5" />
                  <span>{isTaskPanelOpen ? 'Hide Tasks' : 'Show Tasks'}</span>
                  {isTaskPanelOpen ? (
                    <LuX className="w-4 h-4" />
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  )}
                </button>

              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/30 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <LuUsers className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{allUsers.length}</p>
                    <p className="text-sm text-gray-600">Total Users</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/30 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <LuUserCheck className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{friends.length}</p>
                    <p className="text-sm text-gray-600">Your Friends</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/30 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <LuTrendingUp className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {allUsers.filter(user => (user.points || 0) > 50).length}
                    </p>
                    <p className="text-sm text-gray-600">Top Performers</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/30 shadow-lg">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <LuCalendar className="w-6 h-6 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-gray-900">
                      {allUsers.filter(user => {
                        const joinDate = new Date(user.createdAt);
                        const weekAgo = new Date();
                        weekAgo.setDate(weekAgo.getDate() - 7);
                        return joinDate > weekAgo;
                      }).length}
                    </p>
                    <p className="text-sm text-gray-600">New This Week</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Collaboration Request Manager */}
          <div className="mb-8">
            <FriendRequestManager
              refreshTrigger={friendRequestRefreshTrigger}
              onFriendshipChange={async () => {
                // Refresh friends list when friendship changes
                const friendsRes = await axiosInstance.get("/api/users/friends");
                setFriends(friendsRes.data);
              }}
            />
          </div>

          {/* Search and Filter Controls */}
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl p-6 border border-gray-200/30 shadow-lg mb-6">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              {/* Search Bar */}
              <div className="relative flex-1 max-w-md">
                <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search users by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              {/* View Toggle */}
              <div className="flex items-center gap-4">
                <div className="flex bg-gray-100 rounded-xl p-1">
                  <button
                    onClick={() => setView("friends")}
                    className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                      view === "friends"
                        ? "bg-white text-blue-600 shadow-md"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <LuUserCheck size={16} />
                      Friends ({friends.length})
                    </div>
                  </button>
                  <button
                    onClick={() => setView("all")}
                    className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                      view === "all"
                        ? "bg-white text-blue-600 shadow-md"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <LuUsers size={16} />
                      Discover ({(() => {
                        const friendIds = friends.map(friend => friend._id);
                        return allUsers.filter(user =>
                          user._id !== currentUser?._id &&
                          !friendIds.includes(user._id) &&
                          !sentFriendRequests.has(user._id)
                        ).length;
                      })()})
                    </div>
                  </button>
                </div>

                {/* Filter Button */}
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 ${
                    showFilters
                      ? "bg-blue-600 text-white shadow-lg"
                      : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  }`}
                >
                  <LuFilter size={16} />
                  Filters
                </button>

                {/* View Mode Toggle */}
                <div className="flex bg-gray-100 rounded-xl p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded-lg transition-all duration-200 ${
                      viewMode === "grid"
                        ? "bg-white text-blue-600 shadow-md"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <LuGrid3X3 size={16} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded-lg transition-all duration-200 ${
                      viewMode === "list"
                        ? "bg-white text-blue-600 shadow-md"
                        : "text-gray-600 hover:text-gray-900"
                    }`}
                  >
                    <LuList size={16} />
                  </button>
                </div>
              </div>
            </div>

            {/* Advanced Filters */}
            {showFilters && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="name">Name (A-Z)</option>
                      <option value="points">Points (High to Low)</option>
                      <option value="tasks">Tasks Completed</option>
                      <option value="recent">Recently Joined</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Filter By Activity</label>
                    <select
                      value={filterBy}
                      onChange={(e) => setFilterBy(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">All Users</option>
                      <option value="active">Active Users</option>
                      <option value="new">New Users (This Week)</option>
                      <option value="top">Top Performers (50+ Points)</option>
                    </select>
                  </div>
                  <div className="flex items-end">
                    <button
                      onClick={() => {
                        setSearchTerm("");
                        setSortBy("name");
                        setFilterBy("all");
                      }}
                      className="w-full px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all duration-200"
                    >
                      Clear Filters
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Main Content Area with Three Columns */}
          <div className="flex gap-6 relative">
            {/* Left Column - Users Display */}
            <div className={`transition-all duration-300 ${isTaskPanelOpen ? 'flex-1 pr-6' : 'flex-1'}`}>
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600 font-medium">Loading amazing people...</p>
                  </div>
                </div>
              ) : (
                <div className={`${
                  viewMode === "grid"
                    ? selectedFriend
                      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4"
                      : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                    : "space-y-4"
                }`}>
                  {displayedUsers.length > 0 ? (
                    displayedUsers.map((user) => (
                      <EnhancedUserCard
                        key={user._id}
                        user={user}
                        currentUser={currentUser}
                        friends={friends}
                        sentFriendRequests={sentFriendRequests}
                        onAddFriend={handleAddFriend}
                        onRemoveFriend={handleRemoveFriend}
                        selectedUsers={selectedUsers}
                        setSelectedUsers={setSelectedUsers}
                        viewMode={viewMode}
                        onFriendSelect={handleFriendSelect}
                        isSelected={selectedFriend?._id === user._id}
                        onDragOver={handleDragOver}
                        onDrop={handleDrop}
                      />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <LuUsers className="w-12 h-12 text-gray-400" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">No users found</h3>
                      <p className="text-gray-600 mb-4">
                        {searchTerm
                          ? `No users match "${searchTerm}". Try adjusting your search.`
                          : view === "friends"
                            ? "You haven't added any friends yet. Start connecting with your teammates!"
                            : "No users available to display."
                        }
                      </p>
                      {searchTerm && (
                        <button
                          onClick={() => setSearchTerm("")}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200"
                        >
                          Clear Search
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Right Column - Friend's Tasks */}
            {selectedFriend && (
              <div className="w-96 bg-white/70 backdrop-blur-xl rounded-2xl border border-gray-200/30 shadow-lg">
                {/* Task List Header */}
                <div className="p-6 border-b border-gray-200/30">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-bold text-gray-900">
                      Tasks for {selectedFriend.name}
                    </h3>
                    <button
                      onClick={() => {
                        setSelectedFriend(null);
                        setFriendTasks([]);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
                    >
                      <LuX className="w-5 h-5" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-600">
                    Tasks you have assigned to {selectedFriend.name}
                  </p>
                </div>

                {/* Task List Content */}
                <div className="flex-1 overflow-y-auto p-4">
                  {loadingTasks ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                        <p className="text-sm text-gray-600">Loading tasks...</p>
                      </div>
                    </div>
                  ) : friendTasks.length > 0 ? (
                    <div className="p-4 space-y-3">
                      {friendTasks.map((task) => (
                        <div
                          key={task._id}
                          className="bg-white rounded-xl p-4 border border-gray-200 hover:shadow-md transition-all duration-200"
                        >
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-start gap-2 flex-1">
                              {/* Creator Profile Picture */}
                              <img
                                src={task.createdBy?.profileImageUrl || "https://via.placeholder.com/40x40/3B82F6/FFFFFF?text=U"}
                                alt={task.createdBy?.name || "User"}
                                className="w-6 h-6 rounded-full object-cover border border-gray-200 flex-shrink-0"
                                title={`Assigned by ${task.createdBy?.name || "Unknown"}`}
                                onError={(e) => {
                                  e.target.src = "https://via.placeholder.com/40x40/3B82F6/FFFFFF?text=U";
                                }}
                              />
                              <div className="flex-1">
                                <h4 className="font-semibold text-gray-900 text-sm line-clamp-2">
                                  {task.title}
                                </h4>
                                <p className="text-xs text-gray-500">
                                  from {task.createdBy?.name || "Unknown"}
                                </p>
                              </div>
                            </div>
                            <div className={`px-2 py-1 rounded-lg text-xs font-medium ${
                              task.status === 'completed'
                                ? 'bg-green-100 text-green-700'
                                : task.status === 'in-progress'
                                ? 'bg-blue-100 text-blue-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                              {task.status === 'completed' && <LuCheck className="w-3 h-3 inline mr-1" />}
                              {task.status === 'in-progress' && <LuClock className="w-3 h-3 inline mr-1" />}
                              {task.status === 'pending' && <LuCircle className="w-3 h-3 inline mr-1" />}
                              {task.status || 'Pending'}
                            </div>
                          </div>

                          {task.description && (
                            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                              {task.description}
                            </p>
                          )}

                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-2">
                              <span className={`px-2 py-1 rounded-md ${
                                task.priority === 'High'
                                  ? 'bg-red-100 text-red-700'
                                  : task.priority === 'Medium'
                                  ? 'bg-yellow-100 text-yellow-700'
                                  : 'bg-green-100 text-green-700'
                              }`}>
                                {task.priority || 'Low'}
                              </span>
                            </div>
                            {task.dueDate && (
                              <div className="flex items-center gap-1">
                                <LuCalendar className="w-3 h-3" />
                                <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <LuFileSpreadsheet className="w-8 h-8 text-gray-400" />
                        </div>
                        <h4 className="font-semibold text-gray-900 mb-2">No assigned tasks</h4>
                        <p className="text-sm text-gray-600">
                          You haven't assigned any tasks to {selectedFriend.name} yet.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Task Panel - Fixed Position Overlay */}
            {isTaskPanelOpen && (
              <div className="fixed top-20 right-6 w-80 h-[calc(100vh-120px)] bg-white/95 backdrop-blur-xl rounded-2xl border border-gray-200/30 shadow-2xl z-50 flex flex-col transition-all duration-300">
              {/* Task Panel Header */}
              <div className="p-4 border-b border-gray-200/30 flex-shrink-0">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-bold text-gray-900">
                    My Tasks
                  </h3>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={fetchUserTasks}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
                      title="Refresh Tasks"
                    >
                      <LuSearch className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => {
                        console.log('Panel close button clicked');
                        setIsTaskPanelOpen(false);
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
                      title="Close Panel"
                    >
                      <LuX className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  Drag tasks to friends to share them. Friends will receive email & in-app notifications.
                </p>
                {assigningTask && (
                  <div className="flex items-center gap-2 mt-2 text-sm text-blue-600">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span>Assigning task...</span>
                  </div>
                )}
              </div>

              {/* Task List Content */}
              <div className="flex-1 overflow-y-auto">
                {loadingUserTasks ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                      <p className="text-sm text-gray-600">Loading tasks...</p>
                    </div>
                  </div>
                ) : userTasks.length > 0 ? (
                  <div className="p-4 space-y-3">
                    {userTasks.map((task) => (
                      <div
                        key={task._id}
                        draggable
                        onDragStart={(e) => handleDragStart(e, task)}
                        className="bg-white rounded-xl p-4 border border-gray-200 hover:shadow-md transition-all duration-200 cursor-move hover:border-blue-300"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-start gap-2 flex-1">
                            {/* Creator Profile Picture */}
                            <img
                              src={task.createdBy?.profileImageUrl || "https://via.placeholder.com/40x40/3B82F6/FFFFFF?text=U"}
                              alt={task.createdBy?.name || "User"}
                              className="w-6 h-6 rounded-full object-cover border border-gray-200 flex-shrink-0"
                              title={`Created by ${task.createdBy?.name || "Unknown"}`}
                              onError={(e) => {
                                e.target.src = "https://via.placeholder.com/40x40/3B82F6/FFFFFF?text=U";
                              }}
                            />
                            <div className="flex-1">
                              <h4 className="font-semibold text-gray-900 text-sm line-clamp-2">
                                {task.title}
                              </h4>
                              <p className="text-xs text-gray-500">
                                by {task.createdBy?.name || "Unknown"}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-1 text-gray-400">
                            <span className="text-xs">Drag</span>
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                            </svg>
                          </div>
                        </div>

                        {task.description && (
                          <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                            {task.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded-md ${
                              task.priority === 'High'
                                ? 'bg-red-100 text-red-700'
                                : task.priority === 'Medium'
                                ? 'bg-yellow-100 text-yellow-700'
                                : 'bg-green-100 text-green-700'
                            }`}>
                              {task.priority || 'Low'}
                            </span>
                          </div>
                          {task.dueDate && (
                            <div className="flex items-center gap-1">
                              <LuCalendar className="w-3 h-3" />
                              <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <LuFileSpreadsheet className="w-8 h-8 text-gray-400" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">No tasks available</h4>
                      <p className="text-sm text-gray-600">
                        Create new tasks to share with your friends.
                      </p>
                    </div>
                  </div>
                )}
              </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

// Enhanced User Card Component
const EnhancedUserCard = ({
  user,
  currentUser,
  friends,
  sentFriendRequests,
  onAddFriend,
  onRemoveFriend,
  selectedUsers,
  setSelectedUsers,
  viewMode,
  onFriendSelect,
  isSelected: isSelectedFriend,
  onDragOver,
  onDrop
}) => {
  const isFriend = friends.some(friend => friend._id === user._id);
  const isSelected = selectedUsers.includes(user._id);
  const isRequestSent = sentFriendRequests.has(user._id);

  const toggleSelection = () => {
    if (isSelected) {
      setSelectedUsers(prev => prev.filter(id => id !== user._id));
    } else {
      setSelectedUsers(prev => [...prev, user._id]);
    }
  };

  // Get appropriate emoji for badge based on name
  const getBadgeEmoji = (badgeName) => {
    const emojiMap = {
      "First Friend": "👥",
      "Profile Pioneer": "👤",
      "Task Initiate": "📝",
      "Swift Finisher": "⚡",
      "Detail Detective": "🔍",
      "Priority Master": "⭐",
      "Connection Creator": "🤝",
      "Team Harmonizer": "🎵",
      "Social Catalyst": "🌟",
      "Daily Dedicated": "📅",
      "Weekly Warrior": "🗓️",
      "Streak Specialist": "🔥",
      "Consistency Champion": "💪",
      "Project Pioneer": "🚀",
      "Milestone Achiever": "🎯",
      "Grand Architect": "🏗️",
      "Feature Explorer": "🧭",
      "Skill Sharpener": "🎓",
      "Level Up": "📈"
    };
    return emojiMap[badgeName] || "🏆";
  };

  if (viewMode === "list") {
    return (
      <div
        className="bg-white/70 backdrop-blur-xl rounded-2xl p-4 border border-gray-200/30 shadow-lg hover:shadow-xl transition-all duration-300"
        onDragOver={onDragOver}
        onDrop={(e) => onDrop(e, user._id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={toggleSelection}
              className="w-4 h-4 text-blue-600 bg-white border-2 border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
            />
            <Link to={`/users/${user._id}`} className="flex items-center gap-4 flex-1">
              <img
                src={user.profileImageUrl || "/default-user.png"}
                alt={user.name}
                className="w-12 h-12 rounded-full object-cover border-2 border-gray-200"
              />
              <div>
                <h3 className="font-semibold text-gray-900">{user.name}</h3>
                <p className="text-sm text-gray-600">{user.email}</p>
              </div>
            </Link>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-center">
              <p className="text-lg font-bold text-blue-600">{user.points || 0}</p>
              <p className="text-xs text-gray-500">Points</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-green-600">{user.completedTasks || 0}</p>
              <p className="text-xs text-gray-500">Tasks</p>
            </div>
            {user._id !== currentUser?._id && (
              <button
                onClick={() => {
                  if (isFriend) {
                    onRemoveFriend(user._id);
                  } else if (!isRequestSent) {
                    onAddFriend(user._id);
                  }
                }}
                disabled={isRequestSent && !isFriend}
                className={`px-4 py-2 rounded-lg transition-all duration-200 ${
                  isFriend
                    ? "bg-red-100 text-red-600 hover:bg-red-200"
                    : isRequestSent
                    ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                    : "bg-blue-100 text-blue-600 hover:bg-blue-200"
                }`}
              >
                {isFriend ? (
                  <LuUserX size={16} />
                ) : isRequestSent ? (
                  <LuClock size={16} />
                ) : (
                  <LuUserPlus size={16} />
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white/70 backdrop-blur-xl rounded-2xl p-6 border transition-all duration-300 group ${
        isSelectedFriend
          ? 'border-blue-500 shadow-xl ring-2 ring-blue-200'
          : 'border-gray-200/30 shadow-lg hover:shadow-xl'
      }`}
      onDragOver={onDragOver}
      onDrop={(e) => onDrop(e, user._id)}
    >
      <div className="relative">
        {user._id !== currentUser?._id && (
          <input
            type="checkbox"
            checked={isSelected}
            onChange={toggleSelection}
            className="absolute top-0 right-0 w-4 h-4 text-blue-600 bg-white border-2 border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
          />
        )}

        <Link to={`/users/${user._id}`} className="block">
          <div className="text-center mb-4">
            <img
              src={user.profileImageUrl || "/default-user.png"}
              alt={user.name}
              className="w-20 h-20 rounded-full object-cover mx-auto mb-3 border-4 border-gray-200 group-hover:border-blue-300 transition-all duration-300"
            />
            <h3 className="font-bold text-gray-900 text-lg mb-1">{user.name}</h3>
            <p className="text-sm text-gray-600 mb-2">{user.email}</p>

            {/* Level and Points */}
            <div className="flex items-center justify-center gap-4 mb-3">
              <div className="text-center">
                <p className="text-lg font-bold text-blue-600">{user.level || 1}</p>
                <p className="text-xs text-gray-500">Level</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-purple-600">{user.points || 0}</p>
                <p className="text-xs text-gray-500">Points</p>
              </div>
              <div className="text-center">
                <p className="text-lg font-bold text-green-600">
                  {(user.pendingTasks || 0) + (user.inProgressTasks || 0) + (user.completedTasks || 0)}
                </p>
                <p className="text-xs text-gray-500">Tasks</p>
              </div>
            </div>

            {/* Badges Preview */}
            {user.badges && user.badges.length > 0 && (
              <div className="flex justify-center gap-1 mb-4">
                {user.badges.slice(0, 3).map((badge, index) => {
                  const badgeName = typeof badge === 'string' ? badge : badge.name;
                  return (
                    <div
                      key={index}
                      className="w-8 h-8 bg-gradient-to-br from-blue-100 to-purple-100 border border-blue-200 rounded-lg flex items-center justify-center text-sm"
                      title={badgeName}
                    >
                      {getBadgeEmoji(badgeName)}
                    </div>
                  );
                })}
                {user.badges.length > 3 && (
                  <div className="w-8 h-8 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center text-xs text-gray-600">
                    +{user.badges.length - 3}
                  </div>
                )}
              </div>
            )}
          </div>
        </Link>

        {/* Action Buttons */}
        <div className="flex gap-2">
          {user._id !== currentUser?._id && (
            <>
              <button
                onClick={() => {
                  if (isFriend) {
                    onRemoveFriend(user._id);
                  } else if (!isRequestSent) {
                    onAddFriend(user._id);
                  }
                }}
                disabled={isRequestSent && !isFriend}
                className={`flex-1 px-3 py-2 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${
                  isFriend
                    ? "bg-red-100 text-red-600 hover:bg-red-200"
                    : isRequestSent
                    ? "bg-gray-100 text-gray-500 cursor-not-allowed"
                    : "bg-blue-100 text-blue-600 hover:bg-blue-200"
                }`}
              >
                {isFriend ? (
                  <>
                    <LuUserX size={16} />
                    <span className="text-sm">Remove</span>
                  </>
                ) : isRequestSent ? (
                  <>
                    <LuClock size={16} />
                    <span className="text-sm">Requested</span>
                  </>
                ) : (
                  <>
                    <LuUserPlus size={16} />
                    <span className="text-sm">Add</span>
                  </>
                )}
              </button>

              {/* View Tasks Button - Only for friends */}
              {isFriend && (
                <button
                  onClick={() => onFriendSelect(user)}
                  className={`px-3 py-2 rounded-lg transition-all duration-200 flex items-center justify-center gap-1 ${
                    isSelectedFriend
                      ? "bg-green-100 text-green-600 hover:bg-green-200"
                      : "bg-purple-100 text-purple-600 hover:bg-purple-200"
                  }`}
                  title="View Tasks"
                >
                  <LuFileSpreadsheet size={16} />
                  <span className="text-sm">{isSelectedFriend ? "Hide" : "Tasks"}</span>
                </button>
              )}

              <button
                onClick={() => window.open(`mailto:${user.email}`, '_blank')}
                className="px-3 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all duration-200"
                title="Send Email"
              >
                <LuMail size={16} />
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ManageUsers;
