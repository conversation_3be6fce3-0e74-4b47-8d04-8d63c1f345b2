import Task from "../models/Task.js";
import User from "../models/User.js";
import Notification from "../models/Notification.js";
import NotificationService from "../utils/notificationService.js";

// Utility function to sync existing tasks with user references
const syncTaskUserReferences = async () => {
  try {
    console.log("Starting task-user reference sync...");

    // Clear all existing task references
    await User.updateMany({}, {
      $set: {
        createdTasks: [],
        assignedTasks: []
      }
    });

    // Get all tasks
    const tasks = await Task.find({});

    for (const task of tasks) {
      // Update creator's createdTasks
      if (task.createdBy) {
        await User.findByIdAndUpdate(
          task.createdBy,
          { $addToSet: { createdTasks: task._id } }
        );
      }

      // Update assignees' assignedTasks
      if (task.assignedTo && task.assignedTo.length > 0) {
        await User.updateMany(
          { _id: { $in: task.assignedTo } },
          { $addToSet: { assignedTasks: task._id } }
        );
      }
    }

    console.log(`Synced ${tasks.length} tasks with user references`);
    return { success: true, taskCount: tasks.length };
  } catch (error) {
    console.error("Error syncing task-user references:", error);
    return { success: false, error: error.message };
  }
};

// @desc    Get tasks for the authenticated user (created by or assigned to)
// @route   GET /api/tasks/
// @access  Private
const getTasks = async (req, res) => {
  try {
    const { status } = req.query;
    const userId = req.user._id;

    // Filter tasks that are either created by the user OR assigned to the user
    let filter = {
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    };

    if (status) {
      filter.status = status;
    }

    const tasks = await Task.find(filter).populate(
      "assignedTo",
      "name email profileImageUrl"
    ).populate(
      "createdBy",
      "name email profileImageUrl"
    );

    const tasksWithCompletedCount = await Promise.all(
      tasks.map(async (task) => {
        const completedCount = task.todoChecklist.filter(
          (item) => item.completed
        ).length;
        return { ...task._doc, completedTodoCount: completedCount };
      })
    );

    // Create base filter for user's tasks (without status filter)
    const userTasksFilter = {
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    };

    const allTasks = await Task.countDocuments(userTasksFilter);
    const pendingTasks = await Task.countDocuments({
      ...userTasksFilter,
      status: "Pending",
    });
    const inProgressTasks = await Task.countDocuments({
      ...userTasksFilter,
      status: "In Progress",
    });
    const completedTasks = await Task.countDocuments({
      ...userTasksFilter,
      status: "Completed",
    });

    res.json({
      tasks: tasksWithCompletedCount,
      statusSummary: {
        all: allTasks,
        pendingTasks,
        inProgressTasks,
        completedTasks,
      },
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get task by ID (only if user is creator or assigned)
// @route   GET /api/tasks/:id
// @access  Private
const getTaskById = async (req, res) => {
  try {
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: req.params.id,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    }).populate(
      "assignedTo",
      "name email profileImageUrl"
    ).populate(
      "createdBy",
      "name email profileImageUrl"
    );

    if (!task) return res.status(404).json({ message: "Task not found or access denied" });

    res.json(task);
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Create a new task (any authenticated user)
// @route   POST /api/tasks/
// @access  Private
const createTask = async (req, res) => {
  try {
    const {
      title,
      description,
      priority,
      dueDate,
      assignedTo,
      attachments,
      todoChecklist,
    } = req.body;

    // Debug logging
    console.log("=== TASK CREATION DEBUG ===");
    console.log("Request body:", JSON.stringify(req.body, null, 2));
    console.log("User:", req.user._id);
    console.log("Attachments received:", attachments);
    console.log("Attachments type:", typeof attachments);
    console.log("Attachments length:", attachments?.length);

    // Validation
    if (!title || !description || !dueDate) {
      return res.status(400).json({
        message: "Title, description, and due date are required"
      });
    }

    if (!Array.isArray(assignedTo)) {
      return res
        .status(400)
        .json({ message: "assignedTo must be an array of user IDs" });
    }

    // Ensure attachments is an array
    const processedAttachments = Array.isArray(attachments) ? attachments : [];
    console.log("Processed attachments:", processedAttachments);

    // Create task data object
    const taskData = {
      title,
      description,
      priority,
      dueDate,
      assignedTo,
      createdBy: req.user._id,
      todoChecklist: todoChecklist || [],
      attachments: processedAttachments,
      // Add response time tracking
      assignedAt: assignedTo && assignedTo.length > 0 ? new Date() : null,
    };

    console.log("Final task data:", JSON.stringify(taskData, null, 2));

    const task = await Task.create(taskData);

    // Update user references
    try {
      // Add task to creator's createdTasks array
      await User.findByIdAndUpdate(
        req.user._id,
        { $addToSet: { createdTasks: task._id } }
      );

      // Add task to assigned users' assignedTasks arrays
      if (assignedTo && assignedTo.length > 0) {
        await User.updateMany(
          { _id: { $in: assignedTo } },
          { $addToSet: { assignedTasks: task._id } }
        );
      }

      console.log("User references updated successfully");
    } catch (userUpdateError) {
      console.error("Error updating user references:", userUpdateError);
      // Don't fail the task creation if user reference update fails
    }

    // Create notifications for assigned users
    try {
      if (assignedTo && assignedTo.length > 0) {
        await NotificationService.createTaskAssignmentNotification(task, assignedTo, req.user._id);
      }
    } catch (notificationError) {
      console.error("Error creating notifications:", notificationError);
      // Don't fail the task creation if notification creation fails
    }

    console.log("Task created successfully:", task._id);
    res.status(201).json({ message: "Task created successfully", task });
  } catch (error) {
    console.error("Error creating task:", error);
    res.status(500).json({
      message: "Server error",
      error: error.message,
      details: error.stack
    });
  }
};

// @desc    Update task details (only if user is creator or assigned)
// @route   PUT /api/tasks/:id
// @access  Private
const updateTask = async (req, res) => {
  try {
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: req.params.id,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    });

    if (!task) return res.status(404).json({ message: "Task not found or access denied" });

    task.title = req.body.title || task.title;
    task.description = req.body.description || task.description;
    task.priority = req.body.priority || task.priority;
    task.dueDate = req.body.dueDate || task.dueDate;
    task.todoChecklist = req.body.todoChecklist || task.todoChecklist;
    task.attachments = req.body.attachments || task.attachments;

    // Handle assignment changes
    if (req.body.assignedTo) {
      if (!Array.isArray(req.body.assignedTo)) {
        return res
          .status(400)
          .json({ message: "assignedTo must be an array of user IDs" });
      }

      const oldAssignees = task.assignedTo || [];
      const newAssignees = req.body.assignedTo;

      // Update task assignment
      task.assignedTo = newAssignees;

      // Update assignedAt timestamp if new users are being assigned
      const addedAssignees = newAssignees.filter(id => !oldAssignees.some(oldId => oldId.toString() === id));
      if (addedAssignees.length > 0) {
        task.assignedAt = new Date(); // Update timestamp when new assignments are made
      }

      // Update user references
      try {
        // Remove task from old assignees who are no longer assigned
        const removedAssignees = oldAssignees.filter(id => !newAssignees.includes(id.toString()));
        if (removedAssignees.length > 0) {
          await User.updateMany(
            { _id: { $in: removedAssignees } },
            { $pull: { assignedTasks: task._id } }
          );
        }

        // Add task to new assignees
        const addedAssignees = newAssignees.filter(id => !oldAssignees.some(oldId => oldId.toString() === id));
        if (addedAssignees.length > 0) {
          await User.updateMany(
            { _id: { $in: addedAssignees } },
            { $addToSet: { assignedTasks: task._id } }
          );
        }

        console.log("User assignment references updated successfully");
      } catch (userUpdateError) {
        console.error("Error updating user assignment references:", userUpdateError);
      }

      // Create notifications for newly assigned users
      try {
        const addedAssignees = newAssignees.filter(id => !oldAssignees.some(oldId => oldId.toString() === id));
        if (addedAssignees.length > 0) {
          await NotificationService.createTaskAssignmentNotification(task, addedAssignees, userId);
          console.log("Task assignment notifications sent to newly assigned users");
        }
      } catch (notificationError) {
        console.error("Error creating task assignment notifications:", notificationError);
        // Don't fail the task update if notification creation fails
      }
    }

    const updatedTask = await task.save();
    res.json({ message: "Task updated successfully", updatedTask });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Delete a task (only if user is creator or assigned)
// @route   DELETE /api/tasks/:id
// @access  Private
const deleteTask = async (req, res) => {
  try {
    const userId = req.user._id;

    console.log("=== DELETE TASK DEBUG ===");
    console.log("Task ID:", req.params.id);
    console.log("User ID:", userId);

    const task = await Task.findOne({
      _id: req.params.id,
      $or: [
        { createdBy: userId },        // Creator can delete
        { assignedTo: { $in: [userId] } }  // Assigned users can delete
      ]
    });

    console.log("Task found:", task ? "YES" : "NO");
    if (task) {
      console.log("Task creator:", task.createdBy);
      console.log("Task assigned to:", task.assignedTo);
      console.log("User is creator:", task.createdBy.toString() === userId.toString());
      console.log("User is assigned:", task.assignedTo.some(assignee => assignee.toString() === userId.toString()));
    }

    if (!task) return res.status(404).json({ message: "Task not found or access denied" });

    const isCreator = task.createdBy.toString() === userId.toString();
    const isAssigned = task.assignedTo.some(assignee => assignee.toString() === userId.toString());

    if (isCreator) {
      // Creator is deleting - remove task completely for everyone
      console.log("Creator is deleting task - removing completely");

      try {
        // Remove task from creator's createdTasks array
        await User.findByIdAndUpdate(
          task.createdBy,
          { $pull: { createdTasks: task._id } }
        );

        // Remove task from assigned users' assignedTasks arrays
        if (task.assignedTo && task.assignedTo.length > 0) {
          await User.updateMany(
            { _id: { $in: task.assignedTo } },
            { $pull: { assignedTasks: task._id } }
          );
        }

        console.log("User references cleaned up successfully");
      } catch (userUpdateError) {
        console.error("Error cleaning up user references:", userUpdateError);
      }

      await task.deleteOne();
      res.json({ message: "Task deleted successfully" });

    } else if (isAssigned) {
      // Assigned user is deleting - remove only themselves from the task
      console.log("Assigned user is removing themselves from task");

      try {
        // Remove user from task's assignedTo array
        task.assignedTo = task.assignedTo.filter(assignee => assignee.toString() !== userId.toString());
        await task.save();

        // Remove task from user's assignedTasks array
        await User.findByIdAndUpdate(
          userId,
          { $pull: { assignedTasks: task._id } }
        );

        console.log("User removed from task successfully");
        res.json({ message: "Task removed from your list successfully" });

      } catch (userUpdateError) {
        console.error("Error removing user from task:", userUpdateError);
        res.status(500).json({ message: "Server error", error: userUpdateError.message });
      }

    } else {
      res.status(403).json({ message: "You don't have permission to delete this task" });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Update task status (only if user is creator or assigned)
// @route   PUT /api/tasks/:id/status
// @access  Private
const updateTaskStatus = async (req, res) => {
  try {
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: req.params.id,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    });

    if (!task) return res.status(404).json({ message: "Task not found or access denied" });

    const oldStatus = task.status;
    task.status = req.body.status || task.status;

    const wasCompleted = oldStatus === "Completed";
    const isNowCompleted = req.body.status === "Completed";

    // Track response time milestones
    if (oldStatus === "Pending" && req.body.status === "In Progress") {
      // User accepted/started the task
      if (!task.acceptedAt) {
        task.acceptedAt = new Date();
      }
      if (!task.startedAt) {
        task.startedAt = new Date();
      }
    } else if (req.body.status === "In Progress" && !task.startedAt) {
      // Task started
      task.startedAt = new Date();
    } else if (isNowCompleted && !wasCompleted) {
      // Task completed
      task.completedAt = new Date();
    }

    // Update last response time
    task.lastResponseAt = new Date();

    if (task.status === "Completed") {
      task.todoChecklist.forEach((item) => (item.completed = true));
      task.progress = 100;
    }

    await task.save();
    await rewardUserForTask(req.user._id);

    // Create notifications for task completion
    try {
      if (isNowCompleted && !wasCompleted) {
        await NotificationService.createTaskCompletionNotification(task, userId);
      }
    } catch (notificationError) {
      console.error("Error creating completion notifications:", notificationError);
      // Don't fail the status update if notification creation fails
    }

    res.json({ message: "Task status updated", task });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Update task checklist (only if user is creator or assigned)
// @route   PUT /api/tasks/:id/todo
// @access  Private
const updateTaskChecklist = async (req, res) => {
  try {
    const { todoChecklist } = req.body;
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: req.params.id,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    });

    if (!task) return res.status(404).json({ message: "Task not found or access denied" });

    task.todoChecklist = todoChecklist;

    const completedCount = task.todoChecklist.filter(
      (item) => item.completed
    ).length;
    const totalItems = task.todoChecklist.length;
    task.progress =
      totalItems > 0 ? Math.round((completedCount / totalItems) * 100) : 0;

    if (task.progress === 100) {
      task.status = "Completed";
      await rewardUserForTask(req.user._id);
    } else if (task.progress > 0) {
      task.status = "In Progress";
    } else {
      task.status = "Pending";
    }

    await task.save();
    const updatedTask = await Task.findById(req.params.id).populate(
      "assignedTo",
      "name email profileImageUrl"
    );

    res.json({ message: "Task checklist updated", task: updatedTask });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Dashboard Data (all users same data)
// @route   GET /api/tasks/dashboard-data
// @access  Private
const getDashboardData = async (req, res) => {
  try {
    const totalTasks = await Task.countDocuments();
    const pendingTasks = await Task.countDocuments({ status: "Pending" });
    const completedTasks = await Task.countDocuments({ status: "Completed" });
    const overdueTasks = await Task.countDocuments({
      status: { $ne: "Completed" },
      dueDate: { $lt: new Date() },
    });

    const taskStatuses = ["Pending", "In Progress", "Completed"];
    const taskDistributionRaw = await Task.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
    ]);
    const taskDistribution = taskStatuses.reduce((acc, status) => {
      const formattedKey = status.replace(/\s+/g, "");
      acc[formattedKey] =
        taskDistributionRaw.find((item) => item._id === status)?.count || 0;
      return acc;
    }, {});
    taskDistribution["All"] = totalTasks;

    const taskPriorities = ["Low", "Medium", "High"];
    const taskPriorityLevelsRaw = await Task.aggregate([
      { $group: { _id: "$priority", count: { $sum: 1 } } },
    ]);
    const taskPriorityLevels = taskPriorities.reduce((acc, priority) => {
      acc[priority] =
        taskPriorityLevelsRaw.find((item) => item._id === priority)?.count || 0;
      return acc;
    }, {});

    const recentTasks = await Task.find({})
      .sort({ dueDate: -1 })
      .limit(3)
      .populate("assignedTo", "name email profileImageUrl");

    res.json({
      taskDistribution,
      taskPriorityLevels,
      pendingTasks,
      completedTasks,
      overdueTasks,
      recentTasks,
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    User-specific dashboard
// @route   GET /api/tasks/user-dashboard
// @access  Private
const getUserDashboardData = async (req, res) => {
  try {
    const userId = req.user._id;

    // Filter for tasks created by or assigned to the user
    const userTasksFilter = {
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    };

    const totalTasks = await Task.countDocuments(userTasksFilter);
    const pendingTasks = await Task.countDocuments({ ...userTasksFilter, status: "Pending" });
    const completedTasks = await Task.countDocuments({ ...userTasksFilter, status: "Completed" });
    const overdueTasks = await Task.countDocuments({
      ...userTasksFilter,
      status: { $ne: "Completed" },
      dueDate: { $lt: new Date() },
    });

    const taskStatuses = ["Pending", "In Progress", "Completed"];
    const taskDistributionRaw = await Task.aggregate([
      {
        $match: {
          $or: [
            { createdBy: userId },
            { assignedTo: { $in: [userId] } }
          ]
        }
      },
      { $group: { _id: "$status", count: { $sum: 1 } } },
    ]);
    const taskDistribution = taskStatuses.reduce((acc, status) => {
      const formattedKey = status.replace(/\s+/g, "");
      acc[formattedKey] =
        taskDistributionRaw.find((item) => item._id === status)?.count || 0;
      return acc;
    }, {});
    taskDistribution["All"] = totalTasks;

    const taskPriorities = ["Low", "Medium", "High"];
    const taskPriorityLevelsRaw = await Task.aggregate([
      {
        $match: {
          $or: [
            { createdBy: userId },
            { assignedTo: { $in: [userId] } }
          ]
        }
      },
      { $group: { _id: "$priority", count: { $sum: 1 } } },
    ]);
    const taskPriorityLevels = taskPriorities.reduce((acc, priority) => {
      acc[priority] =
        taskPriorityLevelsRaw.find((item) => item._id === priority)?.count || 0;
      return acc;
    }, {});

    const recentTasks = await Task.find(userTasksFilter)
      .sort({ createdAt: -1 })
      .limit(10)
      .select("title status priority dueDate createdAt");

    res.status(200).json({
      statistics: {
        totalTasks,
        pendingTasks,
        completedTasks,
        overdueTasks,
      },
      charts: {
        taskDistribution,
        taskPriorityLevels,
      },
      recentTasks,
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// 🔥 Reward system + badge integration
const rewardUserForTask = async (userId) => {
  const user = await User.findById(userId);
  if (!user) return;

  const oldLevel = user.level;
  user.points += 10;

  // Calculate level based on total points (consistent with mission system)
  const newLevel = Math.floor(user.points / 100) + 1;

  // Check if user leveled up
  if (newLevel > user.level) {
    user.level = newLevel;

    if (!user.badges.includes("Level Up")) {
      user.badges.push("Level Up");
    }
  }

  await user.save();

  // Create notifications for gamification events
  try {
    // XP gained notification
    await NotificationService.createGamificationNotification(userId, "XP_GAINED", {
      xpAmount: 10,
      totalPoints: user.points,
      source: "Task Completion"
    });

    // Level up notification
    if (newLevel > oldLevel) {
      await NotificationService.createGamificationNotification(userId, "LEVEL_UP", {
        oldLevel: oldLevel,
        newLevel: newLevel,
        totalPoints: user.points
      });
    }

    console.log('Task completion gamification notifications created');
  } catch (notificationError) {
    console.error('Error creating task completion gamification notifications:', notificationError);
    // Don't fail the task completion if notification creation fails
  }

  await checkAndRewardTaskMissions(user);
};

const checkAndRewardTaskMissions = async (user) => {
  if (!user.completedMissions) {
    user.completedMissions = [];
  }

  const completedTaskCount = await Task.countDocuments({
    assignedTo: user._id,
    status: "Completed",
  });

  let badgeAwarded = false;
  let missionCompleted = false;

  if (
    completedTaskCount >= 10 &&
    !user.completedMissions.includes("10_tasks_completed")
  ) {
    user.completedMissions.push("10_tasks_completed");
    missionCompleted = true;

    if (!user.badges.includes("10 Tasks Completed")) {
      user.badges.push("10 Tasks Completed");
      badgeAwarded = true;
    }
    await user.save();

    // Create notifications for automatic mission/badge rewards
    try {
      if (missionCompleted) {
        await NotificationService.createGamificationNotification(user._id, "MISSION_COMPLETED", {
          missionTitle: "Task Master",
          missionDescription: "Complete 10 tasks",
          pointsAwarded: 0, // No additional points for automatic missions
          totalPoints: user.points
        });
      }

      if (badgeAwarded) {
        await NotificationService.createGamificationNotification(user._id, "BADGE_EARNED", {
          badgeName: "10 Tasks Completed",
          badgeDescription: "Completed 10 tasks successfully",
          badgeRarity: "common"
        });
      }

      console.log('Automatic task mission notifications created');
    } catch (notificationError) {
      console.error('Error creating automatic task mission notifications:', notificationError);
    }
  }
};

// @desc    Sync existing tasks with user references (admin utility)
// @route   POST /api/tasks/sync-user-references
// @access  Private
const syncTaskReferences = async (req, res) => {
  try {
    const result = await syncTaskUserReferences();
    if (result.success) {
      res.json({
        message: "Task-user references synced successfully",
        taskCount: result.taskCount
      });
    } else {
      res.status(500).json({
        message: "Failed to sync task-user references",
        error: result.error
      });
    }
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

export {
  getTasks,
  getUserDashboardData,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  updateTaskStatus,
  updateTaskChecklist,
  getDashboardData,
  rewardUserForTask,
  syncTaskReferences,
};
