// Test script for collaboration ranking system
// This script tests the collaboration ranking functions with sample data

// Sample data for testing
const sampleCurrentUser = {
  _id: "686899e58df03c0034242553",
  name: "<PERSON><PERSON><PERSON>"
};

const sampleFriends = [
  {
    _id: "friend1_id",
    name: "<PERSON>",
    avatar: "avatar1.jpg"
  },
  {
    _id: "friend2_id", 
    name: "<PERSON>",
    avatar: "avatar2.jpg"
  },
  {
    _id: "friend3_id",
    name: "<PERSON>",
    avatar: "avatar3.jpg"
  }
];

const sampleUserTasks = [
  {
    _id: "task1",
    title: "Complete Project A",
    assignedTo: ["friend1_id"],
    createdBy: "686899e58df03c0034242553",
    status: "Completed",
    assignedAt: new Date("2025-01-01T10:00:00Z"),
    acceptedAt: new Date("2025-01-01T12:00:00Z"), // 2 hours response
    completedAt: new Date("2025-01-03T10:00:00Z")
  },
  {
    _id: "task2", 
    title: "Review Documents",
    assignedTo: ["friend1_id"],
    createdBy: "686899e58df03c0034242553",
    status: "Completed",
    assignedAt: new Date("2025-01-05T09:00:00Z"),
    acceptedAt: new Date("2025-01-05T10:00:00Z"), // 1 hour response
    completedAt: new Date("2025-01-06T15:00:00Z")
  },
  {
    _id: "task3",
    title: "Design Mockups", 
    assignedTo: ["friend2_id"],
    createdBy: "686899e58df03c0034242553",
    status: "In Progress",
    assignedAt: new Date("2025-01-07T14:00:00Z"),
    acceptedAt: new Date("2025-01-08T10:00:00Z") // 20 hours response (slow)
  },
  {
    _id: "task4",
    title: "Test Features",
    assignedTo: ["friend2_id"],
    createdBy: "686899e58df03c0034242553", 
    status: "Pending",
    assignedAt: new Date("2025-01-08T16:00:00Z")
    // No acceptedAt - no response yet
  },
  {
    _id: "task5",
    title: "Write Documentation",
    assignedTo: ["friend3_id"],
    createdBy: "686899e58df03c0034242553",
    status: "Completed",
    assignedAt: new Date("2025-01-02T11:00:00Z"),
    acceptedAt: new Date("2025-01-02T11:30:00Z"), // 30 minutes response (very fast)
    completedAt: new Date("2025-01-04T09:00:00Z")
  }
];

// Test functions (copied from ManageUsers.jsx)
const calculateResponseTime = (assignedAt, respondedAt) => {
  if (!assignedAt || !respondedAt) return null;
  const assigned = new Date(assignedAt);
  const responded = new Date(respondedAt);
  return (responded - assigned) / (1000 * 60 * 60); // Convert to hours
};

const formatResponseTime = (hours) => {
  if (hours === null || hours === undefined) return "No data";
  if (hours < 1) return `${Math.round(hours * 60)} min`;
  if (hours < 24) return `${Math.round(hours)} hrs`;
  return `${Math.round(hours / 24)} days`;
};

const getResponseTimeCategory = (averageHours) => {
  if (averageHours === null || averageHours === undefined) 
    return { category: "NO_DATA", color: "gray", icon: "❓" };
  if (averageHours <= 2) return { category: "INSTANT", color: "green", icon: "⚡" };
  if (averageHours <= 8) return { category: "FAST", color: "blue", icon: "🚀" };
  if (averageHours <= 24) return { category: "NORMAL", color: "yellow", icon: "⏰" };
  if (averageHours <= 72) return { category: "SLOW", color: "orange", icon: "🐌" };
  return { category: "VERY_SLOW", color: "red", icon: "🚨" };
};

const calculateCollaborationMetrics = (friend, userTasks, currentUser) => {
  if (!userTasks || !currentUser) return null;

  // Tasks assigned TO the friend by current user
  const tasksAssignedToFriend = userTasks.filter(task => 
    task.assignedTo && task.assignedTo.some(assignee => 
      (typeof assignee === 'string' ? assignee : assignee._id) === friend._id
    ) && task.createdBy === currentUser._id
  );

  // Tasks assigned BY the friend to current user (placeholder for now)
  const tasksFromFriend = []; // TODO: Implement API call to get tasks from friend

  // Calculate metrics
  const totalTasksGiven = tasksAssignedToFriend.length;
  const totalTasksReceived = tasksFromFriend.length;
  const completedTasksByFriend = tasksAssignedToFriend.filter(task => task.status === 'Completed').length;
  const completionRate = totalTasksGiven > 0 ? (completedTasksByFriend / totalTasksGiven) * 100 : 0;

  // Calculate reciprocity ratio
  const reciprocityRatio = totalTasksGiven > 0 ? totalTasksReceived / totalTasksGiven : 0;

  // Calculate average response time
  const tasksWithResponseTime = tasksAssignedToFriend.filter(task => task.assignedAt && task.acceptedAt);
  const totalResponseTime = tasksWithResponseTime.reduce((sum, task) => {
    const responseTime = calculateResponseTime(task.assignedAt, task.acceptedAt);
    return sum + (responseTime || 0);
  }, 0);
  const averageResponseTime = tasksWithResponseTime.length > 0 ? totalResponseTime / tasksWithResponseTime.length : null;

  // Determine free rider risk
  let freeRiderRisk = 'LOW';
  if (reciprocityRatio < 0.3 || completionRate < 50) freeRiderRisk = 'HIGH';
  else if (reciprocityRatio < 0.6 || completionRate < 75) freeRiderRisk = 'MEDIUM';

  // Calculate overall collaboration score (weighted)
  const collaborationScore = Math.round(
    (reciprocityRatio * 30) +           // 30% weight on reciprocity
    (completionRate * 0.4) +            // 40% weight on completion
    ((averageResponseTime ? Math.max(0, 100 - averageResponseTime) : 50) * 0.2) + // 20% weight on response time
    (Math.min(totalTasksGiven + totalTasksReceived, 50) * 0.1) // 10% weight on volume (capped at 50)
  );

  return {
    userId: friend._id,
    name: friend.name,
    avatar: friend.avatar,
    collaborationScore,
    reciprocityRatio,
    completionRate,
    averageResponseTime,
    totalCollaborations: totalTasksGiven + totalTasksReceived,
    tasksGiven: totalTasksGiven,
    tasksReceived: totalTasksReceived,
    freeRiderRisk,
    trend: 'STABLE' // TODO: Calculate trend based on historical data
  };
};

// Run tests
console.log("🧪 Testing Collaboration Ranking System");
console.log("=====================================");

console.log("\n📊 Sample Data:");
console.log("Current User:", sampleCurrentUser.name);
console.log("Friends:", sampleFriends.map(f => f.name).join(", "));
console.log("Tasks:", sampleUserTasks.length);

console.log("\n🔍 Calculating Collaboration Metrics:");
sampleFriends.forEach(friend => {
  const metrics = calculateCollaborationMetrics(friend, sampleUserTasks, sampleCurrentUser);
  if (metrics) {
    console.log(`\n👤 ${metrics.name}:`);
    console.log(`   Score: ${metrics.collaborationScore}/100`);
    console.log(`   Completion Rate: ${metrics.completionRate.toFixed(1)}%`);
    console.log(`   Reciprocity: ${(metrics.reciprocityRatio * 100).toFixed(1)}%`);
    console.log(`   Response Time: ${formatResponseTime(metrics.averageResponseTime)}`);
    console.log(`   Risk Level: ${metrics.freeRiderRisk}`);
    console.log(`   Tasks Given: ${metrics.tasksGiven}, Received: ${metrics.tasksReceived}`);
    
    const responseCategory = getResponseTimeCategory(metrics.averageResponseTime);
    console.log(`   Response Category: ${responseCategory.icon} ${responseCategory.category}`);
  }
});

console.log("\n✅ Test completed successfully!");
