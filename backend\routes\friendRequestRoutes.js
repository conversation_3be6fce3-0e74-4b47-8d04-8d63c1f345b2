import express from "express";
import { protect } from "../middlewares/authMiddleware.js";
import {
  sendFriendRequest,
  acceptFriendRequest,
  declineFriendRequest,
  getReceivedFriendRequests,
  getSentFriendRequests,
  cancelFriendRequest
} from "../controllers/friendRequestController.js";

const router = express.Router();

// All routes require authentication
router.use(protect);

// Collaboration request management routes
router.post("/send/:userId", sendFriendRequest);           // Send collaboration request
router.post("/accept/:requestId", acceptFriendRequest);    // Accept collaboration request
router.post("/decline/:requestId", declineFriendRequest);  // Decline collaboration request
router.delete("/cancel/:requestId", cancelFriendRequest);  // Cancel sent request

// Get collaboration requests
router.get("/received", getReceivedFriendRequests);        // Get received requests
router.get("/sent", getSentFriendRequests);                // Get sent requests

export default router;
