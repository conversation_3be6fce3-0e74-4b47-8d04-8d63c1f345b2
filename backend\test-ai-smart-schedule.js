/**
 * Test Script for AI Smart Schedule Integration
 * Tests the complete integration of attachment analysis and smart scheduling
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:8000';
const TEST_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY3NzY5ZjE5ZjE5ZjE5ZjE5ZjE5ZjE5ZiIsImlhdCI6MTczNjQzMzYwMCwiZXhwIjoxNzM3MDM4NDAwfQ.test'; // Replace with actual token

// Test data
const testTask = {
  title: "Complete Final Year Project Report",
  description: "Write comprehensive report with literature review, methodology, results, and conclusion sections",
  priority: "High",
  dueDate: "2025-01-15",
  attachments: [
    {
      filename: "research_paper.pdf",
      type: "PDF",
      size: 2048000,
      url: "https://example.com/research_paper.pdf"
    },
    {
      filename: "data_analysis.xlsx",
      type: "Excel",
      size: 512000,
      url: "https://example.com/data_analysis.xlsx"
    },
    {
      filename: "presentation_slides.pptx",
      type: "PowerPoint",
      size: 1024000,
      url: "https://example.com/presentation_slides.pptx"
    }
  ],
  todoChecklist: [
    { text: "Review literature sources", completed: false },
    { text: "Analyze collected data", completed: false },
    { text: "Write methodology section", completed: false },
    { text: "Create results visualizations", completed: false },
    { text: "Draft conclusion", completed: false }
  ]
};

/**
 * Test AI Prioritization with Attachment Analysis
 */
async function testAIPrioritization() {
  console.log('\n🧠 Testing AI Prioritization with Attachment Analysis...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/ai/prioritize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': TEST_TOKEN
      },
      body: JSON.stringify(testTask)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ AI Prioritization Success:');
      console.log(`   📊 Recommended Priority: ${result.recommendedPriority}`);
      console.log(`   🎯 Reasoning: ${result.priorityReasoning}`);
      console.log(`   📅 Suggested Dates: ${result.suggestedDates?.join(', ')}`);
      
      if (result.attachmentAnalysis) {
        console.log('   📎 Attachment Analysis:');
        result.attachmentAnalysis.forEach((attachment, index) => {
          console.log(`     ${index + 1}. ${attachment.filename}`);
          console.log(`        - Complexity: ${attachment.complexity}`);
          console.log(`        - Processing Time: ${attachment.processingTime} minutes`);
          console.log(`        - Cognitive Load: ${attachment.cognitiveLoad}`);
        });
      }
      
      if (result.smartScheduling) {
        console.log('   🗓️ Smart Scheduling:');
        console.log(`     - Total Complexity: ${result.smartScheduling.totalComplexity}`);
        console.log(`     - Total Processing Time: ${result.smartScheduling.totalProcessingTime} minutes`);
        console.log(`     - Optimal Start Dates: ${result.smartScheduling.optimalStartDates?.join(', ')}`);
      }
      
      return result;
    } else {
      console.error('❌ AI Prioritization Failed:', response.status, response.statusText);
      return null;
    }
  } catch (error) {
    console.error('❌ AI Prioritization Error:', error.message);
    return null;
  }
}

/**
 * Test Smart Schedule Generation
 */
async function testSmartScheduleGeneration() {
  console.log('\n📅 Testing Smart Schedule Generation...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/smart-schedule/task/test-task-id`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': TEST_TOKEN
      },
      body: JSON.stringify(testTask)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Smart Schedule Generation Success:');
      console.log(`   📊 Success: ${result.success}`);
      console.log(`   💬 Message: ${result.message}`);
      
      if (result.data) {
        const schedule = result.data;
        console.log('   🗓️ Schedule Details:');
        console.log(`     - Complexity Level: ${schedule.complexityLevel}`);
        console.log(`     - Total Estimated Time: ${schedule.totalEstimatedTime} minutes`);
        console.log(`     - Optimal Start Dates: ${schedule.optimalStartDates?.join(', ')}`);
        
        if (schedule.timeBlockedSchedule) {
          console.log('     - Time Blocked Schedule:');
          schedule.timeBlockedSchedule.forEach((block, index) => {
            console.log(`       ${index + 1}. ${block.startTime} - ${block.endTime}: ${block.activity}`);
          });
        }
        
        if (schedule.workSessions) {
          console.log('     - Work Sessions:');
          schedule.workSessions.forEach((session, index) => {
            console.log(`       ${index + 1}. ${session.type}: ${session.duration} minutes`);
          });
        }
        
        if (schedule.productivityTips) {
          console.log('     - Productivity Tips:');
          schedule.productivityTips.forEach((tip, index) => {
            console.log(`       ${index + 1}. ${tip}`);
          });
        }
      }
      
      return result;
    } else {
      console.error('❌ Smart Schedule Generation Failed:', response.status, response.statusText);
      return null;
    }
  } catch (error) {
    console.error('❌ Smart Schedule Generation Error:', error.message);
    return null;
  }
}

/**
 * Test Attachment Analysis
 */
async function testAttachmentAnalysis() {
  console.log('\n📎 Testing Attachment Analysis...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/smart-schedule/analyze-attachments`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': TEST_TOKEN
      },
      body: JSON.stringify({
        attachments: testTask.attachments
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Attachment Analysis Success:');
      console.log(`   📊 Success: ${result.success}`);
      console.log(`   💬 Message: ${result.message}`);
      
      if (result.data) {
        const analysis = result.data;
        console.log('   📎 Analysis Results:');
        console.log(`     - Total Complexity: ${analysis.totalComplexity}`);
        console.log(`     - Total Processing Time: ${analysis.totalProcessingTime} minutes`);
        
        if (analysis.attachmentAnalysis) {
          console.log('     - Individual Attachments:');
          analysis.attachmentAnalysis.forEach((attachment, index) => {
            console.log(`       ${index + 1}. ${attachment.filename}`);
            console.log(`          - Type: ${attachment.type}`);
            console.log(`          - Complexity: ${attachment.complexity}`);
            console.log(`          - Processing Time: ${attachment.processingTime} minutes`);
            console.log(`          - Cognitive Load: ${attachment.cognitiveLoad}`);
            console.log(`          - Insights: ${attachment.insights}`);
          });
        }
        
        if (analysis.schedulingRecommendations) {
          console.log('     - Scheduling Recommendations:');
          analysis.schedulingRecommendations.forEach((rec, index) => {
            console.log(`       ${index + 1}. ${rec}`);
          });
        }
      }
      
      return result;
    } else {
      console.error('❌ Attachment Analysis Failed:', response.status, response.statusText);
      return null;
    }
  } catch (error) {
    console.error('❌ Attachment Analysis Error:', error.message);
    return null;
  }
}

/**
 * Test Bulk Schedule Generation
 */
async function testBulkScheduleGeneration() {
  console.log('\n📚 Testing Bulk Schedule Generation...');
  
  const multipleTasks = [
    testTask,
    {
      ...testTask,
      title: "Prepare Presentation",
      description: "Create slides and practice presentation",
      priority: "Medium",
      attachments: [testTask.attachments[2]] // Only PowerPoint
    },
    {
      ...testTask,
      title: "Code Review",
      description: "Review and refactor application code",
      priority: "Low",
      attachments: [] // No attachments
    }
  ];
  
  try {
    const response = await fetch(`${BASE_URL}/api/smart-schedule/bulk`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': TEST_TOKEN
      },
      body: JSON.stringify({
        tasks: multipleTasks
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Bulk Schedule Generation Success:');
      console.log(`   📊 Success: ${result.success}`);
      console.log(`   💬 Message: ${result.message}`);
      console.log(`   📚 Generated ${result.data.schedules.length} schedules`);
      
      if (result.data.errors && result.data.errors.length > 0) {
        console.log('   ⚠️ Errors:');
        result.data.errors.forEach((error, index) => {
          console.log(`     ${index + 1}. ${error.taskTitle}: ${error.error}`);
        });
      }
      
      return result;
    } else {
      console.error('❌ Bulk Schedule Generation Failed:', response.status, response.statusText);
      return null;
    }
  } catch (error) {
    console.error('❌ Bulk Schedule Generation Error:', error.message);
    return null;
  }
}

/**
 * Run All Tests
 */
async function runAllTests() {
  console.log('🚀 Starting AI Smart Schedule Integration Tests...');
  console.log('=' .repeat(60));
  
  const results = {
    aiPrioritization: await testAIPrioritization(),
    smartSchedule: await testSmartScheduleGeneration(),
    attachmentAnalysis: await testAttachmentAnalysis(),
    bulkSchedule: await testBulkScheduleGeneration()
  };
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 Test Results Summary:');
  console.log(`   🧠 AI Prioritization: ${results.aiPrioritization ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   📅 Smart Schedule: ${results.smartSchedule ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   📎 Attachment Analysis: ${results.attachmentAnalysis ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   📚 Bulk Schedule: ${results.bulkSchedule ? '✅ PASSED' : '❌ FAILED'}`);
  
  const passedTests = Object.values(results).filter(result => result !== null).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! AI Smart Schedule integration is working perfectly!');
  } else {
    console.log('⚠️ Some tests failed. Please check the error messages above.');
  }
}

// Run the tests
runAllTests().catch(console.error);
