/* Collaboration Request Manager Styles */
.friend-request-manager {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tab Navigation */
.request-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-button {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  background: #e9ecef;
  color: #333;
}

.tab-button.active {
  color: #007bff;
  background: white;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #007bff;
}

/* Request Content */
.request-content {
  padding: 20px;
}

.request-content h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* No Requests State */
.no-requests {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-requests p {
  margin: 0;
  font-size: 16px;
}

/* Request List */
.request-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Request Card */
.request-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.request-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Request Info */
.request-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

/* User Avatar */
.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 20px;
}

/* Request Details */
.request-details {
  flex: 1;
}

.request-details h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.request-details .email {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
}

.request-details .message {
  margin: 0 0 8px 0;
  color: #555;
  font-style: italic;
  font-size: 14px;
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.request-details .timestamp {
  margin: 0;
  color: #888;
  font-size: 12px;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-top: 5px;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* Request Actions */
.request-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.request-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Accept Button */
.accept-btn {
  background: #28a745;
  color: white;
}

.accept-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* Decline Button */
.decline-btn {
  background: #dc3545;
  color: white;
}

.decline-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Cancel Button */
.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .request-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .request-info {
    width: 100%;
  }
  
  .request-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .tab-button {
    font-size: 14px;
    padding: 12px 15px;
  }
}
