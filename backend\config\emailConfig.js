import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

// Gmail SMTP configuration
const createTransporter = () => {
  return nodemailer.createTransport({
    service: 'gmail',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.GMAIL_USER, // Your Gmail address
      pass: process.env.GMAIL_APP_PASSWORD, // Your Gmail App Password (not regular password)
    },
    tls: {
      rejectUnauthorized: false
    },
    // Add connection timeout and retry settings
    connectionTimeout: 60000, // 60 seconds
    greetingTimeout: 30000, // 30 seconds
    socketTimeout: 60000, // 60 seconds
    // Add debug for troubleshooting
    debug: false,
    logger: false
  });
};

// Verify email configuration
export const verifyEmailConfig = async () => {
  try {
    console.log('🔍 Verifying email configuration...');
    console.log('📧 Gmail User:', process.env.GMAIL_USER);
    console.log('🔑 Gmail App Password:', process.env.GMAIL_APP_PASSWORD ? '***configured***' : 'NOT SET');

    const transporter = createTransporter();
    await transporter.verify();
    console.log('✅ Email configuration verified successfully');
    return true;
  } catch (error) {
    console.error('❌ Email configuration error:', error.message);
    console.error('❌ Full error:', error);
    return false;
  }
};

// Send email function
export const sendEmail = async (emailOptions) => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: {
        name: 'Task Manager Pro',
        address: process.env.GMAIL_USER
      },
      to: emailOptions.to,
      subject: emailOptions.subject,
      html: emailOptions.html,
      text: emailOptions.text, // Fallback for clients that don't support HTML
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('❌ Error sending email:', error.message);
    return { success: false, error: error.message };
  }
};

// Test email function
export const sendTestEmail = async (recipientEmail) => {
  const emailOptions = {
    to: recipientEmail,
    subject: 'Task Manager Pro - Email Configuration Test',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
          <h1 style="color: white; margin: 0; font-size: 28px;">🎉 Email Test Successful!</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; border-left: 4px solid #667eea;">
          <h2 style="color: #333; margin-top: 0;">Congratulations!</h2>
          <p style="color: #666; font-size: 16px; line-height: 1.6;">
            Your Task Manager Pro email notification system is now configured and working perfectly. 
            You'll receive email notifications for important task updates, assignments, and system announcements.
          </p>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #fff; border: 1px solid #e9ecef; border-radius: 10px;">
          <h3 style="color: #333; margin-top: 0;">What's Next?</h3>
          <ul style="color: #666; line-height: 1.8;">
            <li>✅ Email notifications are now active</li>
            <li>📧 You'll receive updates for task assignments</li>
            <li>🔔 Get notified about task completions</li>
            <li>🎯 Receive gamification achievements</li>
          </ul>
        </div>
        
        <div style="margin-top: 30px; text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
          <p style="color: #666; margin: 0; font-size: 14px;">
            This email was sent by Task Manager Pro<br>
            If you didn't expect this email, please contact your administrator.
          </p>
        </div>
      </div>
    `,
    text: `
      🎉 Email Test Successful!
      
      Congratulations! Your Task Manager Pro email notification system is now configured and working perfectly.
      
      You'll receive email notifications for:
      - Task assignments and updates
      - Task completions
      - Gamification achievements
      - System announcements
      
      This email was sent by Task Manager Pro.
    `
  };

  return await sendEmail(emailOptions);
};

export default { createTransporter, verifyEmailConfig, sendEmail, sendTestEmail };
