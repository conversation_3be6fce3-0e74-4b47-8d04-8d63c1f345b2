// Load environment variables
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";

// ESM-compatible __dirname setup
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env file from root directory
dotenv.config({ path: path.join(__dirname, ".env") });
console.log("✅ GEMINI_API_KEY:", process.env.GEMINI_API_KEY);

// Import other dependencies
import express from "express";
import cors from "cors";
import connectDB from "./config/db.js";

// Import routes
import authRoutes from "./routes/authRoutes.js";
import userRoutes from "./routes/userRoutes.js";
import taskRoutes from "./routes/taskRoutes.js";
import reportRoutes from "./routes/reportRoutes.js";
import gamificationRoutes from "./routes/gamificationRoutes.js";
import aiRoutes from "./routes/aiRoutes.js";
import notificationRoutes from "./routes/notificationRoutes.js";
import friendRequestRoutes from "./routes/friendRequestRoutes.js";

// Initialize express app
const app = express();

// Enable CORS
app.use(
  cors({
    origin: process.env.CLIENT_URL || "*",
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// Connect to MongoDB
connectDB();

// Middlewares
app.use(express.json());

// Define API routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/tasks", taskRoutes);
app.use("/api/reports", reportRoutes);
app.use("/api/gamification", gamificationRoutes);
app.use("/api/ai", aiRoutes);
app.use("/api/notifications", notificationRoutes);
app.use("/api/friend-requests", friendRequestRoutes);

// Serve uploaded files
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Test email configuration on startup
import { verifyEmailConfig } from "./config/emailConfig.js";

// Start the server
const PORT = process.env.PORT || 8000;
app.listen(PORT, async () => {
  console.log(`🚀 Server running on port ${PORT}`);

  // Test email configuration on startup
  console.log("🔍 Testing email configuration on startup...");
  const emailConfigured = await verifyEmailConfig();
  console.log(`📧 Email system status: ${emailConfigured ? '✅ WORKING' : '❌ FAILED'}`);
});
