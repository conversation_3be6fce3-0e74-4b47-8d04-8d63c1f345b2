import React, { useContext } from "react";
import { BrowserRouter as Router, Routes, Route, Outlet, Navigate } from "react-router-dom";
import Login from "./pages/Auth/Login";
import SignUp from "./pages/Auth/SignUp";

import UserDashboard from "./pages/User/UserDashboard.jsx";
import MyTasks from "./pages/User/MyTasks.jsx";
import CreateTask from "./pages/User/CreateTask.jsx"; // Renamed version of Admin CreateTask
import ViewTaskDetails from "./pages/User/ViewTaskDetails.jsx";
import TeamMembers from "./pages/User/ManageUsers.jsx"; // Optional: if you want team members for users
import CollaborationRanking from "./pages/User/CollaborationRanking.jsx";
import UserProfile from "./pages/User/UserProfile";
import Gallery from "./pages/User/Gallery.jsx";
import EmailSettings from "./components/Settings/EmailSettings.jsx";
import NotificationPreferences from "./components/Settings/NotificationPreferences.jsx";
import SettingsDashboard from "./components/Settings/SettingsDashboard.jsx";
import PrivateRoute from "./routes/PrivateRoute";
import UserProvider, { UserContext } from "./context/userContext";
import ThemeProvider from "./context/ThemeContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { Toaster } from "react-hot-toast";

const App = () => {
  return (
    <ThemeProvider>
      <UserProvider>
        <NotificationProvider>
          <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/signUp" element={<SignUp />} />

          {/* Protected Routes for all authenticated users */}
          <Route element={<PrivateRoute />}>
            <Route path="/user/dashboard" element={<UserDashboard />} />
            <Route path="/user/tasks" element={<MyTasks />} />
            <Route path="/user/task-details/:id" element={<ViewTaskDetails />} />
            <Route path="/user/create-task" element={<CreateTask />} />
            <Route path="/user/team" element={<TeamMembers />} /> {/* Optional */}
            <Route path="/user/collaboration-ranking" element={<CollaborationRanking />} />
            <Route path="/user/gallery" element={<Gallery />} />
            <Route path="/user/email-settings" element={<EmailSettings />} />
            <Route path="/user/notification-preferences" element={<NotificationPreferences />} />
            <Route path="/user/settings" element={<SettingsDashboard />} />
            <Route path="/users/:userId" element={<UserProfile />} />
          </Route>

          {/* Default Redirect */}
          <Route path="/" element={<Root />} />
        </Routes>
        </Router>

        <Toaster
          toastOptions={{
            className: "",
            style: { fontSize: "13px" },
          }}
        />
        </NotificationProvider>
      </UserProvider>
    </ThemeProvider>
  );
};

export default App;

const Root = () => {
  const { user, loading } = useContext(UserContext);

  if (loading) return <Outlet />;

  if (!user) return <Navigate to="/login" />;

  return <Navigate to="/user/dashboard" />;
};
