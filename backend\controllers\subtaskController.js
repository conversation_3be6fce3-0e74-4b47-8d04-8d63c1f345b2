import Task from "../models/Task.js";
import User from "../models/User.js";
import NotificationService from "../utils/notificationService.js";

// @desc    Update subtask status with collaboration tracking
// @route   PUT /api/tasks/:taskId/subtasks/:subtaskIndex/status
// @access  Private
const updateSubtaskStatus = async (req, res) => {
  try {
    const { taskId, subtaskIndex } = req.params;
    const { status, estimatedHours, actualHours, comment } = req.body;
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: taskId,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    }).populate('todoChecklist.assignedTo todoChecklist.completedBy todoChecklist.startedBy', 'name email');

    if (!task) {
      return res.status(404).json({ message: "Task not found or access denied" });
    }

    const subtask = task.todoChecklist[subtaskIndex];
    if (!subtask) {
      return res.status(404).json({ message: "Subtask not found" });
    }

    // Update subtask based on status
    const now = new Date();
    
    switch (status) {
      case 'in_progress':
        subtask.status = 'in_progress';
        subtask.startedBy = userId;
        subtask.startedAt = now;
        if (estimatedHours) subtask.estimatedHours = estimatedHours;
        break;
        
      case 'completed':
        subtask.status = 'completed';
        subtask.completed = true;
        subtask.completedBy = userId;
        subtask.completedAt = now;
        if (actualHours) subtask.actualHours = actualHours;
        break;
        
      case 'pending':
        subtask.status = 'pending';
        subtask.completed = false;
        subtask.startedBy = null;
        subtask.startedAt = null;
        subtask.completedBy = null;
        subtask.completedAt = null;
        break;
    }

    // Add comment if provided
    if (comment) {
      subtask.comments.push({
        user: userId,
        text: comment,
        timestamp: now
      });
    }

    // Update overall task progress
    const completedCount = task.todoChecklist.filter(item => item.completed).length;
    const totalItems = task.todoChecklist.length;
    task.progress = totalItems > 0 ? Math.round((completedCount / totalItems) * 100) : 0;

    // Update task status based on progress
    if (task.progress === 100) {
      task.status = "Completed";
      task.completedAt = now;
    } else if (task.progress > 0) {
      task.status = "In Progress";
    } else {
      task.status = "Pending";
    }

    await task.save();

    // Create notifications for collaboration
    try {
      if (status === 'completed') {
        await NotificationService.createSubtaskCompletionNotification(task, subtask, userId);
      }
    } catch (notificationError) {
      console.error("Error creating subtask completion notifications:", notificationError);
    }

    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'name email profileImageUrl')
      .populate('todoChecklist.assignedTo todoChecklist.completedBy todoChecklist.startedBy', 'name email profileImageUrl');

    res.json({ 
      message: "Subtask updated successfully", 
      task: updatedTask,
      subtask: updatedTask.todoChecklist[subtaskIndex]
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Assign subtask to user
// @route   PUT /api/tasks/:taskId/subtasks/:subtaskIndex/assign
// @access  Private
const assignSubtask = async (req, res) => {
  try {
    const { taskId, subtaskIndex } = req.params;
    const { assignedTo } = req.body;
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: taskId,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    });

    if (!task) {
      return res.status(404).json({ message: "Task not found or access denied" });
    }

    const subtask = task.todoChecklist[subtaskIndex];
    if (!subtask) {
      return res.status(404).json({ message: "Subtask not found" });
    }

    subtask.assignedTo = assignedTo;
    await task.save();

    // Create notification for subtask assignment
    try {
      await NotificationService.createSubtaskAssignmentNotification(task, subtask, userId, assignedTo);
    } catch (notificationError) {
      console.error("Error creating subtask assignment notifications:", notificationError);
    }

    const updatedTask = await Task.findById(taskId)
      .populate('assignedTo', 'name email profileImageUrl')
      .populate('todoChecklist.assignedTo todoChecklist.completedBy todoChecklist.startedBy', 'name email profileImageUrl');

    res.json({ 
      message: "Subtask assigned successfully", 
      task: updatedTask,
      subtask: updatedTask.todoChecklist[subtaskIndex]
    });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// @desc    Get subtask collaboration analytics
// @route   GET /api/tasks/:taskId/subtasks/analytics
// @access  Private
const getSubtaskAnalytics = async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user._id;

    const task = await Task.findOne({
      _id: taskId,
      $or: [
        { createdBy: userId },
        { assignedTo: { $in: [userId] } }
      ]
    }).populate('todoChecklist.assignedTo todoChecklist.completedBy todoChecklist.startedBy', 'name email profileImageUrl');

    if (!task) {
      return res.status(404).json({ message: "Task not found or access denied" });
    }

    // Calculate analytics
    const analytics = {
      totalSubtasks: task.todoChecklist.length,
      completedSubtasks: task.todoChecklist.filter(item => item.completed).length,
      inProgressSubtasks: task.todoChecklist.filter(item => item.status === 'in_progress').length,
      pendingSubtasks: task.todoChecklist.filter(item => item.status === 'pending').length,
      
      // Collaboration metrics
      contributorStats: {},
      estimatedVsActualHours: {
        totalEstimated: 0,
        totalActual: 0
      },
      
      // Timeline data
      completionTimeline: []
    };

    // Calculate contributor statistics
    task.todoChecklist.forEach(subtask => {
      if (subtask.completedBy) {
        const contributorId = subtask.completedBy._id.toString();
        if (!analytics.contributorStats[contributorId]) {
          analytics.contributorStats[contributorId] = {
            user: subtask.completedBy,
            completed: 0,
            totalEstimatedHours: 0,
            totalActualHours: 0
          };
        }
        analytics.contributorStats[contributorId].completed++;
        analytics.contributorStats[contributorId].totalEstimatedHours += subtask.estimatedHours || 0;
        analytics.contributorStats[contributorId].totalActualHours += subtask.actualHours || 0;
      }
      
      analytics.estimatedVsActualHours.totalEstimated += subtask.estimatedHours || 0;
      analytics.estimatedVsActualHours.totalActual += subtask.actualHours || 0;
      
      if (subtask.completedAt) {
        analytics.completionTimeline.push({
          subtaskText: subtask.text,
          completedBy: subtask.completedBy,
          completedAt: subtask.completedAt,
          actualHours: subtask.actualHours
        });
      }
    });

    // Sort timeline by completion date
    analytics.completionTimeline.sort((a, b) => new Date(a.completedAt) - new Date(b.completedAt));

    res.json({ analytics });
  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

export { updateSubtaskStatus, assignSubtask, getSubtaskAnalytics };
