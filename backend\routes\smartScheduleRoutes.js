import express from 'express';
import {
  generateTaskSchedule,
  generateBulkSchedules,
  analyzeAttachments,
  getOptimalSchedule
} from '../controllers/smartScheduleController.js';
import { protect } from '../middlewares/authMiddleware.js';

const router = express.Router();

/**
 * Smart Schedule Routes
 * All routes require authentication
 */

// Generate smart schedule for a specific task
router.post('/task/:taskId', protect, generateTaskSchedule);

// Generate smart schedules for multiple tasks
router.post('/bulk', protect, generateBulkSchedules);

// Analyze attachments for scheduling
router.post('/analyze-attachments', protect, analyzeAttachments);

// Get optimal schedule for date range
router.post('/optimal', protect, getOptimalSchedule);

export default router;
