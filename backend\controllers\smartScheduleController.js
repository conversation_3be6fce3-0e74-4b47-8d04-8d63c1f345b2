import SmartScheduler from '../services/smartScheduler.js';
import AttachmentAnalyzer from '../services/attachmentAnalyzer.js';

/**
 * Smart Schedule Controller
 * Handles API endpoints for AI-powered task scheduling
 */

const smartScheduler = new SmartScheduler();
const attachmentAnalyzer = new AttachmentAnalyzer();

/**
 * Generate smart schedule for a specific task
 */
export const generateTaskSchedule = async (req, res) => {
  try {
    const { taskId } = req.params;
    const { 
      title, 
      description, 
      priority, 
      dueDate, 
      attachments = [], 
      todoChecklist = [] 
    } = req.body;

    console.log(`🧠 Generating smart schedule for task: ${title}`);

    // Create task data object
    const taskData = {
      id: taskId,
      title,
      description,
      priority,
      dueDate,
      attachments,
      todoChecklist
    };

    // Get AI analysis first
    const aiAnalysisResponse = await fetch('http://localhost:8000/api/ai/prioritize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': req.headers.authorization
      },
      body: JSON.stringify({
        title,
        description,
        priority,
        dueDate,
        attachments,
        todoChecklist
      })
    });

    let aiAnalysis = {};
    if (aiAnalysisResponse.ok) {
      aiAnalysis = await aiAnalysisResponse.json();
    }

    // Generate comprehensive smart schedule
    const smartSchedule = await smartScheduler.generateSmartSchedule(taskData, aiAnalysis);

    console.log(`✅ Smart schedule generated for task: ${title}`);

    res.status(200).json({
      success: true,
      message: 'Smart schedule generated successfully',
      data: smartSchedule
    });

  } catch (error) {
    console.error('❌ Smart Schedule Generation Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate smart schedule',
      error: error.message
    });
  }
};

/**
 * Generate smart schedules for multiple tasks
 */
export const generateBulkSchedules = async (req, res) => {
  try {
    const { tasks } = req.body;

    if (!tasks || !Array.isArray(tasks)) {
      return res.status(400).json({
        success: false,
        message: 'Tasks array is required'
      });
    }

    console.log(`🧠 Generating smart schedules for ${tasks.length} tasks`);

    const schedules = [];
    const errors = [];

    // Process each task
    for (const task of tasks) {
      try {
        // Get AI analysis for each task
        const aiAnalysisResponse = await fetch('http://localhost:8000/api/ai/prioritize', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': req.headers.authorization
          },
          body: JSON.stringify({
            title: task.title,
            description: task.description,
            priority: task.priority,
            dueDate: task.dueDate,
            attachments: task.attachments || [],
            todoChecklist: task.todoChecklist || []
          })
        });

        let aiAnalysis = {};
        if (aiAnalysisResponse.ok) {
          aiAnalysis = await aiAnalysisResponse.json();
        }

        // Generate smart schedule
        const smartSchedule = await smartScheduler.generateSmartSchedule(task, aiAnalysis);
        schedules.push(smartSchedule);

      } catch (taskError) {
        console.error(`❌ Error processing task ${task.title}:`, taskError);
        errors.push({
          taskId: task.id,
          taskTitle: task.title,
          error: taskError.message
        });
      }
    }

    console.log(`✅ Generated ${schedules.length} smart schedules`);

    res.status(200).json({
      success: true,
      message: `Generated ${schedules.length} smart schedules`,
      data: {
        schedules,
        errors: errors.length > 0 ? errors : undefined
      }
    });

  } catch (error) {
    console.error('❌ Bulk Smart Schedule Generation Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate bulk smart schedules',
      error: error.message
    });
  }
};

/**
 * Analyze attachment complexity for scheduling
 */
export const analyzeAttachments = async (req, res) => {
  try {
    const { attachments } = req.body;

    if (!attachments || !Array.isArray(attachments)) {
      return res.status(400).json({
        success: false,
        message: 'Attachments array is required'
      });
    }

    console.log(`📎 Analyzing ${attachments.length} attachments`);

    const analysis = await attachmentAnalyzer.analyzeTaskAttachments(attachments);

    console.log(`✅ Attachment analysis completed`);

    res.status(200).json({
      success: true,
      message: 'Attachment analysis completed',
      data: analysis
    });

  } catch (error) {
    console.error('❌ Attachment Analysis Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze attachments',
      error: error.message
    });
  }
};

/**
 * Get optimal work schedule for a date range
 */
export const getOptimalSchedule = async (req, res) => {
  try {
    const { startDate, endDate, tasks } = req.body;

    if (!startDate || !endDate || !tasks) {
      return res.status(400).json({
        success: false,
        message: 'Start date, end date, and tasks are required'
      });
    }

    console.log(`📅 Generating optimal schedule from ${startDate} to ${endDate}`);

    // Generate schedules for all tasks
    const schedules = [];
    for (const task of tasks) {
      try {
        // Get AI analysis
        const aiAnalysisResponse = await fetch('http://localhost:8000/api/ai/prioritize', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': req.headers.authorization
          },
          body: JSON.stringify({
            title: task.title,
            description: task.description,
            priority: task.priority,
            dueDate: task.dueDate,
            attachments: task.attachments || [],
            todoChecklist: task.todoChecklist || []
          })
        });

        let aiAnalysis = {};
        if (aiAnalysisResponse.ok) {
          aiAnalysis = await aiAnalysisResponse.json();
        }

        const schedule = await smartScheduler.generateSmartSchedule(task, aiAnalysis);
        schedules.push(schedule);
      } catch (taskError) {
        console.error(`Error processing task ${task.title}:`, taskError);
      }
    }

    // Optimize schedule distribution across date range
    const optimizedSchedule = optimizeScheduleDistribution(schedules, startDate, endDate);

    console.log(`✅ Optimal schedule generated`);

    res.status(200).json({
      success: true,
      message: 'Optimal schedule generated',
      data: {
        schedules: optimizedSchedule,
        dateRange: { startDate, endDate },
        totalTasks: tasks.length
      }
    });

  } catch (error) {
    console.error('❌ Optimal Schedule Generation Error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate optimal schedule',
      error: error.message
    });
  }
};

/**
 * Helper function to optimize schedule distribution
 */
function optimizeScheduleDistribution(schedules, startDate, endDate) {
  // Sort schedules by complexity and due date
  const sortedSchedules = schedules.sort((a, b) => {
    // High complexity tasks first
    if (a.complexityLevel === 'High' && b.complexityLevel !== 'High') return -1;
    if (b.complexityLevel === 'High' && a.complexityLevel !== 'High') return 1;
    
    // Then by estimated time (longer tasks first)
    return b.totalEstimatedTime - a.totalEstimatedTime;
  });

  // Distribute across available days
  const start = new Date(startDate);
  const end = new Date(endDate);
  const availableDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  
  const optimized = sortedSchedules.map((schedule, index) => {
    const dayOffset = index % availableDays;
    const scheduledDate = new Date(start);
    scheduledDate.setDate(scheduledDate.getDate() + dayOffset);
    
    return {
      ...schedule,
      optimizedStartDate: scheduledDate.toISOString().split('T')[0],
      distributionReason: `Distributed across ${availableDays} days for optimal workload balance`
    };
  });

  return optimized;
}

export default {
  generateTaskSchedule,
  generateBulkSchedules,
  analyzeAttachments,
  getOptimalSchedule
};
