import dotenv from "dotenv";
dotenv.config();

import { GoogleGenerativeAI } from "@google/generative-ai"; // ✅ Correct package name

import {
  generatePrioritizationPrompt,
  generatePrioritizationExplanationPrompt,
} from "../utils/prompt.js";



console.log("✅ Gemini API Key:", process.env.GEMINI_API_KEY);
const ai = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

const generatePrioritization = async (req, res) => {
  try {
    const { title, description, priority, dueDate, assignedTo, attachments, todoChecklist } = req.body;

    const prompt = generatePrioritizationPrompt(
      title,
      description,
      priority,
      dueDate,
      assignedTo,
      attachments,
      todoChecklist
    );

    console.log("🧠 Enhanced Prompt Sent to Gemini:\n", prompt);

    const model = ai.getGenerativeModel({ model: "gemini-1.5-pro" });
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text().trim();

    console.log("📩 Gemini Enhanced Response:\n", text);

    // Parse enhanced response format
    const parseResponseLine = (text, prefix) => {
      const line = text.split("\n").find((line) => line.startsWith(prefix));
      return line?.split(prefix)[1]?.trim() || null;
    };

    const aiPriority = parseResponseLine(text, "AI Priority:");
    const userPriority = parseResponseLine(text, "User Priority:");
    const priorityMatch = parseResponseLine(text, "Priority Match:");
    const recommendedPriority = parseResponseLine(text, "Recommended Priority:");
    const priorityReasoning = parseResponseLine(text, "Priority Reasoning:");

    const estimatedDuration = parseResponseLine(text, "Estimated Duration:");
    const suggestedDatesRaw = parseResponseLine(text, "Suggested Start Date(s):");
    const startDateReasoning = parseResponseLine(text, "Start Date Reasoning:");

    const workloadBreakdown = parseResponseLine(text, "Workload Breakdown:");
    const riskAssessment = parseResponseLine(text, "Risk Assessment:");

    // Process suggested dates
    let suggestedDates = suggestedDatesRaw
      ?.split(",")
      .map((d) => d.trim()) || [];

    // Filter out past dates
    const today = new Date();
    suggestedDates = suggestedDates.filter((dateStr) => {
      try {
        const [day, month, year] = dateStr.split("/").map(Number);
        const date = new Date(year, month - 1, day);
        return date >= today;
      } catch {
        return false;
      }
    });

    // Fallback to tomorrow if no valid dates
    if (suggestedDates.length === 0) {
      const fallback = new Date();
      fallback.setDate(fallback.getDate() + 1);
      const day = String(fallback.getDate()).padStart(2, '0');
      const month = String(fallback.getMonth() + 1).padStart(2, '0');
      const year = fallback.getFullYear();
      suggestedDates = [`${day}/${month}/${year}`];
    }

    // Enhanced response with priority reconciliation
    res.status(200).json({
      // Priority Analysis
      aiPriority: aiPriority || "Medium",
      userPriority: userPriority || priority,
      priorityMatch: priorityMatch || "UNKNOWN",
      recommendedPriority: recommendedPriority || aiPriority || "Medium",
      priorityReasoning: priorityReasoning || "Analysis not available",

      // Scheduling
      suggestedDates,
      startDateReasoning: startDateReasoning || "Optimal timing based on due date",
      estimatedDuration: estimatedDuration || "Not specified",

      // Analysis
      workloadBreakdown: workloadBreakdown || "Standard complexity",
      riskAssessment: riskAssessment || "Low risk",

      // Legacy support
      priority: recommendedPriority || aiPriority || "Medium"
    });
  } catch (error) {
    console.error("❌ AI Error:", error);
    res.status(500).json({
      message: "Failed to generate Task Prioritization",
      error: error.message,
    });
  }
};




const generatePrioritizationExplanation = async (req, res) => {
  try {
    const { title, description, priority, dueDate, assignedTo, attachments, todoChecklist } =
      req.body;

    const prompt = generatePrioritizationExplanationPrompt(
      title,
      description,
      priority,
      dueDate,
      assignedTo,
      attachments,
      todoChecklist
    );

    const model = ai.getGenerativeModel({ model: "gemini-1.5-pro" });

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text().trim();

    res.status(200).json({ explanation: text });
  } catch (error) {
    res.status(500).json({
      message: "Failed to generate Task Prioritization Explanation",
      error: error.message,
    });
  }
};

 export {
  generatePrioritization,
  generatePrioritizationExplanation,
};
