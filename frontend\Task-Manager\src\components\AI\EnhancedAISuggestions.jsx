import React from 'react';
import { LuBrain, LuClock, LuInfo, LuCheck, LuX, LuCalendar, LuActivity } from 'react-icons/lu';

const AISuggestions = ({ suggestion, onApplyPriority, onScheduleTask }) => {
  if (!suggestion) return null;

  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getMatchIcon = (match) => {
    switch (match) {
      case 'ALIGNED': return <LuCheck className="w-4 h-4 text-green-600" />;
      case 'CONFLICT': return <LuX className="w-4 h-4 text-red-600" />;
      default: return <LuInfo className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getMatchColor = (match) => {
    switch (match) {
      case 'ALIGNED': return 'border-green-200 bg-green-50';
      case 'CONFLICT': return 'border-red-200 bg-red-50';
      default: return 'border-yellow-200 bg-yellow-50';
    }
  };

  return (
    <div className="mt-4 p-4 bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200/50 rounded-2xl">
      {/* Header */}
      <div className="flex items-center gap-2 mb-4">
        <LuBrain className="w-5 h-5 text-purple-600" />
        <h3 className="font-semibold text-purple-900">AI Task Analysis</h3>
      </div>

      {/* Priority Reconciliation */}
      <div className={`p-3 rounded-xl border mb-4 ${getMatchColor(suggestion.priorityMatch)}`}>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getMatchIcon(suggestion.priorityMatch)}
            <span className="font-medium text-sm">Priority Analysis</span>
          </div>
          {suggestion.priorityMatch === 'CONFLICT' && (
            <button
              onClick={() => onApplyPriority?.(suggestion.recommendedPriority)}
              className="px-3 py-1 text-xs font-semibold text-purple-700 bg-purple-100 hover:bg-purple-200 rounded-lg transition-colors"
            >
              Apply AI Priority
            </button>
          )}
        </div>
        
        <div className="grid grid-cols-2 gap-3 mb-2">
          <div>
            <span className="text-xs text-gray-600">Your Priority:</span>
            <div className={`inline-block px-2 py-1 rounded-lg text-xs font-medium ml-2 ${getPriorityColor(suggestion.userPriority)}`}>
              {suggestion.userPriority}
            </div>
          </div>
          <div>
            <span className="text-xs text-gray-600">AI Suggests:</span>
            <div className={`inline-block px-2 py-1 rounded-lg text-xs font-medium ml-2 ${getPriorityColor(suggestion.aiPriority)}`}>
              {suggestion.aiPriority}
            </div>
          </div>
        </div>
        
        {suggestion.priorityReasoning && (
          <p className="text-xs text-gray-700 mt-2 leading-relaxed">
            <strong>Reasoning:</strong> {suggestion.priorityReasoning}
          </p>
        )}
      </div>

      {/* Scheduling Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="p-3 bg-white/70 rounded-xl border border-blue-200/50">
          <div className="flex items-center gap-2 mb-2">
            <LuCalendar className="w-4 h-4 text-blue-600" />
            <span className="font-medium text-sm text-blue-900">Suggested Start</span>
          </div>
          <div className="text-sm text-blue-700 mb-1">
            {suggestion.suggestedDates?.join(", ") || "Not available"}
          </div>
          {suggestion.startDateReasoning && (
            <p className="text-xs text-blue-600 leading-relaxed">
              {suggestion.startDateReasoning}
            </p>
          )}
          {suggestion.suggestedDates?.length > 0 && (
            <button
              onClick={() => onScheduleTask?.(suggestion.suggestedDates[0])}
              className="mt-2 px-3 py-1 text-xs font-semibold text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-lg transition-colors"
            >
              Schedule Task
            </button>
          )}
        </div>

        <div className="p-3 bg-white/70 rounded-xl border border-green-200/50">
          <div className="flex items-center gap-2 mb-2">
            <LuClock className="w-4 h-4 text-green-600" />
            <span className="font-medium text-sm text-green-900">Time Estimate</span>
          </div>
          <div className="text-sm text-green-700 mb-1">
            {suggestion.estimatedDuration}
          </div>
        </div>
      </div>

      {/* Analysis Details */}
      <div className="space-y-3">
        {suggestion.workloadBreakdown && (
          <div className="p-3 bg-white/70 rounded-xl border border-gray-200/50">
            <div className="flex items-center gap-2 mb-2">
              <LuActivity className="w-4 h-4 text-gray-600" />
              <span className="font-medium text-sm text-gray-900">Workload Analysis</span>
            </div>
            <p className="text-xs text-gray-700 leading-relaxed">
              {suggestion.workloadBreakdown}
            </p>
          </div>
        )}

        {suggestion.riskAssessment && (
          <div className="p-3 bg-white/70 rounded-xl border border-orange-200/50">
            <div className="flex items-center gap-2 mb-2">
              <LuInfo className="w-4 h-4 text-orange-600" />
              <span className="font-medium text-sm text-orange-900">Risk Assessment</span>
            </div>
            <p className="text-xs text-orange-700 leading-relaxed">
              {suggestion.riskAssessment}
            </p>
          </div>
        )}
      </div>

      {/* Explanation Section */}
      {suggestion.explanation && (
        <div className="mt-4 p-3 bg-white/70 rounded-xl border border-indigo-200/50">
          <div className="flex items-center gap-2 mb-2">
            <span className="font-medium text-sm text-indigo-900">💬 Detailed Explanation</span>
          </div>
          <p className="text-xs text-indigo-700 leading-relaxed">
            {suggestion.explanation}
          </p>
        </div>
      )}
    </div>
  );
};

export default AISuggestions;
